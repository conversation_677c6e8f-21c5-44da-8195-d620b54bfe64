import { getLogger } from '../utils/logger';
import { asyncOptimizer, TaskPriority } from '../utils/async-optimizer';

const logger = getLogger();

/**
 * WebRTC统计类型
 */
export enum WebRTCStatsType {
  INBOUND_RTP = 'inbound-rtp',
  OUTBOUND_RTP = 'outbound-rtp',
  REMOTE_INBOUND_RTP = 'remote-inbound-rtp',
  REMOTE_OUTBOUND_RTP = 'remote-outbound-rtp',
  MEDIA_SOURCE = 'media-source',
  CSRC = 'csrc',
  PEER_CONNECTION = 'peer-connection',
  DATA_CHANNEL = 'data-channel',
  TRANSPORT = 'transport',
  CANDIDATE_PAIR = 'candidate-pair',
  LOCAL_CANDIDATE = 'local-candidate',
  REMOTE_CANDIDATE = 'remote-candidate',
  CERTIFICATE = 'certificate',
  ICE_SERVER = 'ice-server',
  TRACK = 'track',
  CODEC = 'codec'
}

/**
 * 媒体类型
 */
export enum MediaType {
  AUDIO = 'audio',
  VIDEO = 'video'
}

/**
 * 媒体方向
 */
export enum MediaDirection {
  SEND = 'send',
  RECEIVE = 'receive'
}

/**
 * 媒体质量
 */
export enum MediaQuality {
  EXCELLENT = 'excellent',
  GOOD = 'good',
  FAIR = 'fair',
  POOR = 'poor',
  BAD = 'bad'
}

/**
 * 网络质量
 */
export enum NetworkQuality {
  EXCELLENT = 'excellent',
  GOOD = 'good',
  FAIR = 'fair',
  POOR = 'poor',
  BAD = 'bad'
}

/**
 * 媒体统计信息
 */
export interface MediaStats {
  type: MediaType;
  direction: MediaDirection;
  codec?: string;
  bitrate?: number;
  packetsPerSecond?: number;
  packetsLost?: number;
  packetsLostPercentage?: number;
  jitter?: number;
  frameRate?: number;
  frameWidth?: number;
  frameHeight?: number;
  framesDropped?: number;
  framesDecoded?: number;
  framesEncoded?: number;
  keyFramesEncoded?: number;
  nackCount?: number;
  pliCount?: number;
  firCount?: number;
  quality?: MediaQuality;
  qualityLimitationReason?: string;
  roundTripTime?: number;
  timestamp: number;
}

/**
 * 网络统计信息
 */
export interface NetworkStats {
  localCandidate?: {
    type: string;
    protocol: string;
    ip: string;
    port: number;
    networkType?: string;
  };
  remoteCandidate?: {
    type: string;
    protocol: string;
    ip: string;
    port: number;
  };
  transport?: {
    bytesReceived: number;
    bytesSent: number;
    packetsReceived: number;
    packetsSent: number;
    dtlsState: string;
    selectedCandidatePairChanges: number;
  };
  candidatePair?: {
    availableOutgoingBitrate?: number;
    availableIncomingBitrate?: number;
    currentRoundTripTime?: number;
    totalRoundTripTime?: number;
    requestsReceived?: number;
    requestsSent?: number;
    responsesReceived?: number;
    responsesSent?: number;
    consentRequestsSent?: number;
    packetsDiscardedOnSend?: number;
    state?: string;
  };
  quality: NetworkQuality;
  timestamp: number;
}

/**
 * 连接统计信息
 */
export interface ConnectionStats {
  id: string;
  connectionState: RTCPeerConnectionState;
  iceConnectionState: RTCIceConnectionState;
  iceGatheringState: RTCIceGatheringState;
  signalingState: RTCSignalingState;
  iceCandidatePoolSize?: number;
  iceRole?: string;
  dtlsRole?: string;
  mediaStats: MediaStats[];
  networkStats: NetworkStats;
  timestamp: number;
}

/**
 * WebRTC统计收集器
 */
export class WebRTCStatsCollector {
  private connections = new Map<string, RTCPeerConnection>();
  private statsHistory = new Map<string, ConnectionStats[]>();
  private collectionIntervals = new Map<string, NodeJS.Timeout>();
  private maxHistoryLength = 100; // 每个连接最多保存100条历史记录

  constructor() {
    logger.info('WebRTC统计收集器已初始化');
  }

  /**
   * 注册连接
   */
  registerConnection(id: string, connection: RTCPeerConnection): void {
    this.connections.set(id, connection);
    this.statsHistory.set(id, []);
    logger.debug(`注册WebRTC连接: ${id}`);
  }

  /**
   * 取消注册连接
   */
  unregisterConnection(id: string): void {
    this.connections.delete(id);
    this.statsHistory.delete(id);
    
    // 清除收集间隔
    const interval = this.collectionIntervals.get(id);
    if (interval) {
      clearInterval(interval);
      this.collectionIntervals.delete(id);
    }
    
    logger.debug(`取消注册WebRTC连接: ${id}`);
  }

  /**
   * 开始收集统计信息
   */
  startCollecting(id: string, intervalMs: number = 5000): void {
    const connection = this.connections.get(id);
    if (!connection) {
      logger.warn(`尝试为未注册的连接收集统计信息: ${id}`);
      return;
    }

    // 清除现有间隔
    const existingInterval = this.collectionIntervals.get(id);
    if (existingInterval) {
      clearInterval(existingInterval);
    }

    // 立即收集一次
    this.collectStats(id);

    // 设置定期收集
    const interval = setInterval(() => {
      this.collectStats(id);
    }, intervalMs);

    this.collectionIntervals.set(id, interval);
    logger.debug(`开始收集WebRTC统计信息: ${id}, 间隔: ${intervalMs}ms`);
  }

  /**
   * 停止收集统计信息
   */
  stopCollecting(id: string): void {
    const interval = this.collectionIntervals.get(id);
    if (interval) {
      clearInterval(interval);
      this.collectionIntervals.delete(id);
      logger.debug(`停止收集WebRTC统计信息: ${id}`);
    }
  }

  /**
   * 收集统计信息
   */
  async collectStats(id: string): Promise<ConnectionStats | null> {
    const connection = this.connections.get(id);
    if (!connection) {
      return null;
    }

    try {
      // 使用异步优化器处理统计收集
      return await asyncOptimizer.addTask(
        async () => {
          const stats = await connection.getStats();
          const parsedStats = this.parseStats(id, connection, stats);
          
          // 保存到历史记录
          const history = this.statsHistory.get(id) || [];
          history.push(parsedStats);
          
          // 限制历史记录长度
          if (history.length > this.maxHistoryLength) {
            history.shift();
          }
          
          this.statsHistory.set(id, history);
          return parsedStats;
        },
        TaskPriority.LOW
      );
    } catch (error) {
      logger.error(`收集WebRTC统计信息失败: ${id}, 错误: ${error}`);
      return null;
    }
  }

  /**
   * 获取最新统计信息
   */
  getLatestStats(id: string): ConnectionStats | null {
    const history = this.statsHistory.get(id);
    if (!history || history.length === 0) {
      return null;
    }
    
    return history[history.length - 1];
  }

  /**
   * 获取统计历史
   */
  getStatsHistory(id: string): ConnectionStats[] {
    return this.statsHistory.get(id) || [];
  }

  /**
   * 获取媒体质量
   */
  getMediaQuality(id: string, type: MediaType): MediaQuality {
    const latestStats = this.getLatestStats(id);
    if (!latestStats) {
      return MediaQuality.FAIR;
    }

    const mediaStats = latestStats.mediaStats.find(
      stats => stats.type === type
    );

    return mediaStats?.quality || MediaQuality.FAIR;
  }

  /**
   * 获取网络质量
   */
  getNetworkQuality(id: string): NetworkQuality {
    const latestStats = this.getLatestStats(id);
    if (!latestStats) {
      return NetworkQuality.FAIR;
    }

    return latestStats.networkStats.quality;
  }

  /**
   * 解析统计信息
   */
  private parseStats(id: string, connection: RTCPeerConnection, stats: RTCStatsReport): ConnectionStats {
    const mediaStats: MediaStats[] = [];
    const networkStats: NetworkStats = {
      quality: NetworkQuality.FAIR,
      timestamp: Date.now()
    };

    // 处理每个统计项
    stats.forEach(stat => {
      switch (stat.type) {
        case WebRTCStatsType.INBOUND_RTP:
          this.parseInboundRtpStats(stat, mediaStats);
          break;
        case WebRTCStatsType.OUTBOUND_RTP:
          this.parseOutboundRtpStats(stat, mediaStats);
          break;
        case WebRTCStatsType.CANDIDATE_PAIR:
          this.parseCandidatePairStats(stat, networkStats);
          break;
        case WebRTCStatsType.LOCAL_CANDIDATE:
          this.parseLocalCandidateStats(stat, networkStats);
          break;
        case WebRTCStatsType.REMOTE_CANDIDATE:
          this.parseRemoteCandidateStats(stat, networkStats);
          break;
        case WebRTCStatsType.TRANSPORT:
          this.parseTransportStats(stat, networkStats);
          break;
      }
    });

    // 计算媒体质量
    this.calculateMediaQuality(mediaStats);
    
    // 计算网络质量
    this.calculateNetworkQuality(networkStats);

    return {
      id,
      connectionState: connection.connectionState,
      iceConnectionState: connection.iceConnectionState,
      iceGatheringState: connection.iceGatheringState,
      signalingState: connection.signalingState,
      mediaStats,
      networkStats,
      timestamp: Date.now()
    };
  }

  /**
   * 解析入站RTP统计信息
   */
  private parseInboundRtpStats(stat: any, mediaStats: MediaStats[]): void {
    const mediaType = stat.kind as MediaType;
    
    const stats: MediaStats = {
      type: mediaType,
      direction: MediaDirection.RECEIVE,
      codec: stat.codecId,
      bitrate: stat.bytesReceived ? (stat.bytesReceived * 8) / 1000 : undefined,
      packetsPerSecond: stat.packetsReceived,
      packetsLost: stat.packetsLost,
      packetsLostPercentage: stat.packetsLost && stat.packetsReceived 
        ? (stat.packetsLost / (stat.packetsLost + stat.packetsReceived)) * 100 
        : 0,
      jitter: stat.jitter,
      frameRate: stat.framesPerSecond,
      frameWidth: stat.frameWidth,
      frameHeight: stat.frameHeight,
      framesDropped: stat.framesDropped,
      framesDecoded: stat.framesDecoded,
      nackCount: stat.nackCount,
      pliCount: stat.pliCount,
      firCount: stat.firCount,
      timestamp: Date.now()
    };
    
    mediaStats.push(stats);
  }

  /**
   * 解析出站RTP统计信息
   */
  private parseOutboundRtpStats(stat: any, mediaStats: MediaStats[]): void {
    const mediaType = stat.kind as MediaType;
    
    const stats: MediaStats = {
      type: mediaType,
      direction: MediaDirection.SEND,
      codec: stat.codecId,
      bitrate: stat.bytesSent ? (stat.bytesSent * 8) / 1000 : undefined,
      packetsPerSecond: stat.packetsSent,
      frameRate: stat.framesPerSecond,
      frameWidth: stat.frameWidth,
      frameHeight: stat.frameHeight,
      framesEncoded: stat.framesEncoded,
      keyFramesEncoded: stat.keyFramesEncoded,
      nackCount: stat.nackCount,
      pliCount: stat.pliCount,
      firCount: stat.firCount,
      qualityLimitationReason: stat.qualityLimitationReason,
      timestamp: Date.now()
    };
    
    mediaStats.push(stats);
  }

  /**
   * 解析候选对统计信息
   */
  private parseCandidatePairStats(stat: any, networkStats: NetworkStats): void {
    if (stat.selected) {
      networkStats.candidatePair = {
        availableOutgoingBitrate: stat.availableOutgoingBitrate,
        availableIncomingBitrate: stat.availableIncomingBitrate,
        currentRoundTripTime: stat.currentRoundTripTime,
        totalRoundTripTime: stat.totalRoundTripTime,
        requestsReceived: stat.requestsReceived,
        requestsSent: stat.requestsSent,
        responsesReceived: stat.responsesReceived,
        responsesSent: stat.responsesSent,
        consentRequestsSent: stat.consentRequestsSent,
        packetsDiscardedOnSend: stat.packetsDiscardedOnSend,
        state: stat.state
      };
    }
  }

  /**
   * 解析本地候选统计信息
   */
  private parseLocalCandidateStats(stat: any, networkStats: NetworkStats): void {
    networkStats.localCandidate = {
      type: stat.candidateType,
      protocol: stat.protocol,
      ip: stat.ip || stat.address,
      port: stat.port,
      networkType: stat.networkType
    };
  }

  /**
   * 解析远程候选统计信息
   */
  private parseRemoteCandidateStats(stat: any, networkStats: NetworkStats): void {
    networkStats.remoteCandidate = {
      type: stat.candidateType,
      protocol: stat.protocol,
      ip: stat.ip || stat.address,
      port: stat.port
    };
  }

  /**
   * 解析传输统计信息
   */
  private parseTransportStats(stat: any, networkStats: NetworkStats): void {
    networkStats.transport = {
      bytesReceived: stat.bytesReceived,
      bytesSent: stat.bytesSent,
      packetsReceived: stat.packetsReceived,
      packetsSent: stat.packetsSent,
      dtlsState: stat.dtlsState,
      selectedCandidatePairChanges: stat.selectedCandidatePairChanges
    };
  }

  /**
   * 计算媒体质量
   */
  private calculateMediaQuality(mediaStats: MediaStats[]): void {
    for (const stats of mediaStats) {
      if (stats.type === MediaType.VIDEO) {
        // 视频质量评估
        if (stats.packetsLostPercentage !== undefined) {
          if (stats.packetsLostPercentage < 1) {
            stats.quality = MediaQuality.EXCELLENT;
          } else if (stats.packetsLostPercentage < 3) {
            stats.quality = MediaQuality.GOOD;
          } else if (stats.packetsLostPercentage < 8) {
            stats.quality = MediaQuality.FAIR;
          } else if (stats.packetsLostPercentage < 15) {
            stats.quality = MediaQuality.POOR;
          } else {
            stats.quality = MediaQuality.BAD;
          }
        }
      } else if (stats.type === MediaType.AUDIO) {
        // 音频质量评估
        if (stats.packetsLostPercentage !== undefined) {
          if (stats.packetsLostPercentage < 0.5) {
            stats.quality = MediaQuality.EXCELLENT;
          } else if (stats.packetsLostPercentage < 2) {
            stats.quality = MediaQuality.GOOD;
          } else if (stats.packetsLostPercentage < 5) {
            stats.quality = MediaQuality.FAIR;
          } else if (stats.packetsLostPercentage < 10) {
            stats.quality = MediaQuality.POOR;
          } else {
            stats.quality = MediaQuality.BAD;
          }
        }
      }
    }
  }

  /**
   * 计算网络质量
   */
  private calculateNetworkQuality(networkStats: NetworkStats): void {
    let quality = NetworkQuality.FAIR;
    
    if (networkStats.candidatePair?.currentRoundTripTime !== undefined) {
      const rtt = networkStats.candidatePair.currentRoundTripTime * 1000; // 转换为毫秒
      
      if (rtt < 50) {
        quality = NetworkQuality.EXCELLENT;
      } else if (rtt < 100) {
        quality = NetworkQuality.GOOD;
      } else if (rtt < 200) {
        quality = NetworkQuality.FAIR;
      } else if (rtt < 400) {
        quality = NetworkQuality.POOR;
      } else {
        quality = NetworkQuality.BAD;
      }
    }
    
    networkStats.quality = quality;
  }
}

// 全局WebRTC统计收集器实例
export const webrtcStatsCollector = new WebRTCStatsCollector();
