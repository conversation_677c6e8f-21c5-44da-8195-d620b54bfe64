import { getLogger } from './logger';

const logger = getLogger();

/**
 * 任务优先级
 */
export enum TaskPriority {
  LOW = 0,
  NORMAL = 1,
  HIGH = 2,
  CRITICAL = 3
}

/**
 * 异步任务
 */
export interface AsyncTask<T = any> {
  id: string;
  priority: TaskPriority;
  task: () => Promise<T>;
  timeout?: number;
  retries?: number;
  createdAt: number;
  resolve: (value: T) => void;
  reject: (error: Error) => void;
}

/**
 * 批处理配置
 */
export interface BatchConfig {
  maxBatchSize: number;
  maxWaitTime: number;
  processor: (items: any[]) => Promise<any[]>;
}

/**
 * 异步处理优化器
 */
export class AsyncOptimizer {
  private taskQueue: AsyncTask[] = [];
  private runningTasks = new Set<string>();
  private batchProcessors = new Map<string, BatchConfig>();
  private batchQueues = new Map<string, any[]>();
  private batchTimers = new Map<string, NodeJS.Timeout>();
  
  private maxConcurrency: number;
  private isProcessing = false;
  private processingTimer?: NodeJS.Timeout;

  constructor(maxConcurrency: number = 10) {
    this.maxConcurrency = maxConcurrency;
    this.startProcessing();
  }

  /**
   * 添加异步任务
   */
  addTask<T>(
    task: () => Promise<T>,
    priority: TaskPriority = TaskPriority.NORMAL,
    options: { timeout?: number; retries?: number } = {}
  ): Promise<T> {
    return new Promise<T>((resolve, reject) => {
      const taskId = `task_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      
      const asyncTask: AsyncTask<T> = {
        id: taskId,
        priority,
        task,
        timeout: options.timeout,
        retries: options.retries || 0,
        createdAt: Date.now(),
        resolve,
        reject
      };

      // 按优先级插入队列
      this.insertTaskByPriority(asyncTask);
      
      logger.debug(`添加异步任务: ${taskId}, 优先级: ${priority}`);
    });
  }

  /**
   * 注册批处理器
   */
  registerBatchProcessor(name: string, config: BatchConfig): void {
    this.batchProcessors.set(name, config);
    this.batchQueues.set(name, []);
    logger.info(`注册批处理器: ${name}`);
  }

  /**
   * 添加批处理项目
   */
  addToBatch<T>(batchName: string, item: T): Promise<T> {
    return new Promise<T>((resolve, reject) => {
      const config = this.batchProcessors.get(batchName);
      if (!config) {
        reject(new Error(`未找到批处理器: ${batchName}`));
        return;
      }

      const queue = this.batchQueues.get(batchName)!;
      const batchItem = { item, resolve, reject };
      queue.push(batchItem);

      // 检查是否需要立即处理
      if (queue.length >= config.maxBatchSize) {
        this.processBatch(batchName);
      } else {
        // 设置定时器
        this.setBatchTimer(batchName);
      }
    });
  }

  /**
   * 创建防抖函数
   */
  debounce<T extends (...args: any[]) => any>(
    func: T,
    delay: number
  ): (...args: Parameters<T>) => Promise<ReturnType<T>> {
    let timeoutId: NodeJS.Timeout;
    let latestResolve: (value: ReturnType<T>) => void;
    let latestReject: (error: Error) => void;

    return (...args: Parameters<T>): Promise<ReturnType<T>> => {
      return new Promise<ReturnType<T>>((resolve, reject) => {
        // 清除之前的定时器
        if (timeoutId) {
          clearTimeout(timeoutId);
        }

        latestResolve = resolve;
        latestReject = reject;

        timeoutId = setTimeout(async () => {
          try {
            const result = await func(...args);
            latestResolve(result);
          } catch (error) {
            latestReject(error instanceof Error ? error : new Error(String(error)));
          }
        }, delay);
      });
    };
  }

  /**
   * 创建节流函数
   */
  throttle<T extends (...args: any[]) => any>(
    func: T,
    delay: number
  ): (...args: Parameters<T>) => Promise<ReturnType<T> | null> {
    let lastExecution = 0;
    let timeoutId: NodeJS.Timeout | null = null;

    return (...args: Parameters<T>): Promise<ReturnType<T> | null> => {
      return new Promise<ReturnType<T> | null>((resolve) => {
        const now = Date.now();
        const timeSinceLastExecution = now - lastExecution;

        if (timeSinceLastExecution >= delay) {
          // 立即执行
          lastExecution = now;
          Promise.resolve(func(...args))
            .then(resolve)
            .catch(() => resolve(null));
        } else {
          // 延迟执行
          if (timeoutId) {
            clearTimeout(timeoutId);
          }

          const remainingTime = delay - timeSinceLastExecution;
          timeoutId = setTimeout(() => {
            lastExecution = Date.now();
            Promise.resolve(func(...args))
              .then(resolve)
              .catch(() => resolve(null));
          }, remainingTime);
        }
      });
    };
  }

  /**
   * 并行执行任务（带并发控制）
   */
  async parallelExecution<T>(
    tasks: (() => Promise<T>)[],
    concurrency: number = this.maxConcurrency
  ): Promise<T[]> {
    const results: T[] = [];
    const executing: Promise<void>[] = [];

    for (let i = 0; i < tasks.length; i++) {
      const task = tasks[i];
      
      const promise = task().then(result => {
        results[i] = result;
      });

      executing.push(promise);

      if (executing.length >= concurrency) {
        await Promise.race(executing);
        // 移除已完成的任务
        const completedIndex = executing.findIndex(p => 
          p === promise || (p as any).isResolved
        );
        if (completedIndex !== -1) {
          executing.splice(completedIndex, 1);
        }
      }
    }

    await Promise.all(executing);
    return results;
  }

  /**
   * 获取队列统计信息
   */
  getStats() {
    const batchStats = Array.from(this.batchQueues.entries()).map(([name, queue]) => ({
      name,
      queueSize: queue.length,
      hasTimer: this.batchTimers.has(name)
    }));

    return {
      taskQueue: this.taskQueue.length,
      runningTasks: this.runningTasks.size,
      maxConcurrency: this.maxConcurrency,
      isProcessing: this.isProcessing,
      batchProcessors: batchStats
    };
  }

  /**
   * 停止处理器
   */
  stop(): void {
    this.isProcessing = false;
    
    if (this.processingTimer) {
      clearInterval(this.processingTimer);
    }

    // 清理批处理定时器
    this.batchTimers.forEach(timer => clearTimeout(timer));
    this.batchTimers.clear();

    // 拒绝所有待处理的任务
    this.taskQueue.forEach(task => {
      task.reject(new Error('异步处理器已停止'));
    });
    this.taskQueue = [];

    logger.info('异步处理优化器已停止');
  }

  /**
   * 按优先级插入任务
   */
  private insertTaskByPriority(task: AsyncTask): void {
    let insertIndex = this.taskQueue.length;
    
    for (let i = 0; i < this.taskQueue.length; i++) {
      if (this.taskQueue[i].priority < task.priority) {
        insertIndex = i;
        break;
      }
    }
    
    this.taskQueue.splice(insertIndex, 0, task);
  }

  /**
   * 开始处理任务
   */
  private startProcessing(): void {
    this.isProcessing = true;
    
    this.processingTimer = setInterval(() => {
      this.processNextTask();
    }, 10); // 每10ms检查一次
  }

  /**
   * 处理下一个任务
   */
  private async processNextTask(): Promise<void> {
    if (this.runningTasks.size >= this.maxConcurrency || this.taskQueue.length === 0) {
      return;
    }

    const task = this.taskQueue.shift();
    if (!task) {
      return;
    }

    this.runningTasks.add(task.id);

    try {
      // 设置超时
      let timeoutId: NodeJS.Timeout | undefined;
      const timeoutPromise = task.timeout ? new Promise<never>((_, reject) => {
        timeoutId = setTimeout(() => {
          reject(new Error(`任务超时: ${task.id}`));
        }, task.timeout);
      }) : null;

      // 执行任务
      const taskPromise = task.task();
      const result = timeoutPromise 
        ? await Promise.race([taskPromise, timeoutPromise])
        : await taskPromise;

      if (timeoutId) {
        clearTimeout(timeoutId);
      }

      task.resolve(result);
      logger.debug(`任务完成: ${task.id}`);

    } catch (error) {
      if (task.retries && task.retries > 0) {
        // 重试任务
        task.retries--;
        this.insertTaskByPriority(task);
        logger.debug(`任务重试: ${task.id}, 剩余重试次数: ${task.retries}`);
      } else {
        task.reject(error instanceof Error ? error : new Error(String(error)));
        logger.error(`任务失败: ${task.id}, 错误: ${error}`);
      }
    } finally {
      this.runningTasks.delete(task.id);
    }
  }

  /**
   * 设置批处理定时器
   */
  private setBatchTimer(batchName: string): void {
    if (this.batchTimers.has(batchName)) {
      return;
    }

    const config = this.batchProcessors.get(batchName)!;
    const timer = setTimeout(() => {
      this.processBatch(batchName);
    }, config.maxWaitTime);

    this.batchTimers.set(batchName, timer);
  }

  /**
   * 处理批次
   */
  private async processBatch(batchName: string): Promise<void> {
    const config = this.batchProcessors.get(batchName);
    const queue = this.batchQueues.get(batchName);
    
    if (!config || !queue || queue.length === 0) {
      return;
    }

    // 清除定时器
    const timer = this.batchTimers.get(batchName);
    if (timer) {
      clearTimeout(timer);
      this.batchTimers.delete(batchName);
    }

    // 取出要处理的项目
    const batchItems = queue.splice(0, config.maxBatchSize);
    const items = batchItems.map(item => item.item);

    try {
      const results = await config.processor(items);
      
      // 返回结果
      batchItems.forEach((batchItem, index) => {
        batchItem.resolve(results[index]);
      });

      logger.debug(`批处理完成: ${batchName}, 处理项目数: ${batchItems.length}`);
    } catch (error) {
      // 批处理失败，拒绝所有项目
      batchItems.forEach(batchItem => {
        batchItem.reject(error instanceof Error ? error : new Error(String(error)));
      });

      logger.error(`批处理失败: ${batchName}, 错误: ${error}`);
    }
  }
}

// 全局异步优化器实例
export const asyncOptimizer = new AsyncOptimizer();
