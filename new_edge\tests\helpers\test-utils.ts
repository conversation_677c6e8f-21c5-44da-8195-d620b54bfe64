import { StreamManager } from '../../src/stream/manager';
import { AuthManager } from '../../src/auth/auth-manager';
import { ServerConfig, ClientInfo } from '../../src/types';

/**
 * 创建测试用的服务器配置
 */
export function createTestConfig(): ServerConfig {
  return {
    http: {
      listen: '127.0.0.1:0', // 使用随机端口
      public: 'http://localhost:8080',
      cors: true
    },
    webrtc: {
      iceServers: [{ urls: 'stun:stun.l.google.com:19302' }],
      maxBitrate: 1000000
    },
    log: {
      level: 'ERROR' // 在测试中减少日志输出
    },
    cascade: {
      mode: 'manual',
      nodes: []
    },
    auth: {
      enabled: false,
      secret: 'test-secret',
      tokenExpiry: 3600
    }
  };
}

/**
 * 创建测试用的流管理器
 */
export function createTestStreamManager(): StreamManager {
  return new StreamManager([{ urls: 'stun:stun.l.google.com:19302' }]);
}

/**
 * 创建测试用的认证管理器
 */
export function createTestAuthManager(): AuthManager {
  return new AuthManager({
    enabled: true,
    secret: 'test-secret',
    tokenExpiry: 3600,
    bcryptRounds: 4 // 降低加密轮数以加快测试速度
  });
}

/**
 * 创建测试用的客户端信息
 */
export function createTestClientInfo(overrides: Partial<ClientInfo> = {}): ClientInfo {
  return {
    id: `test-client-${Date.now()}`,
    ip: '127.0.0.1',
    userAgent: 'test-agent',
    startTime: Date.now(),
    type: 'publisher',
    ...overrides
  };
}

/**
 * 创建模拟的SDP offer
 */
export function createMockSdpOffer(): string {
  return `v=0
o=- 123456789 123456789 IN IP4 127.0.0.1
s=-
t=0 0
m=video 9 UDP/TLS/RTP/SAVPF 96
c=IN IP4 127.0.0.1
a=rtcp:9 IN IP4 127.0.0.1
a=ice-ufrag:test
a=ice-pwd:testpassword
a=fingerprint:sha-256 00:00:00:00:00:00:00:00:00:00:00:00:00:00:00:00:00:00:00:00:00:00:00:00:00:00:00:00:00:00:00:00
a=setup:actpass
a=mid:0
a=sendrecv
a=rtcp-mux
a=rtpmap:96 VP8/90000
m=audio 9 UDP/TLS/RTP/SAVPF 111
c=IN IP4 127.0.0.1
a=rtcp:9 IN IP4 127.0.0.1
a=ice-ufrag:test
a=ice-pwd:testpassword
a=fingerprint:sha-256 00:00:00:00:00:00:00:00:00:00:00:00:00:00:00:00:00:00:00:00:00:00:00:00:00:00:00:00:00:00:00:00
a=setup:actpass
a=mid:1
a=sendrecv
a=rtcp-mux
a=rtpmap:111 opus/48000/2`;
}

/**
 * 创建模拟的SDP answer
 */
export function createMockSdpAnswer(): string {
  return `v=0
o=- 987654321 987654321 IN IP4 127.0.0.1
s=-
t=0 0
m=video 9 UDP/TLS/RTP/SAVPF 96
c=IN IP4 127.0.0.1
a=rtcp:9 IN IP4 127.0.0.1
a=ice-ufrag:answer
a=ice-pwd:answerpassword
a=fingerprint:sha-256 11:11:11:11:11:11:11:11:11:11:11:11:11:11:11:11:11:11:11:11:11:11:11:11:11:11:11:11:11:11:11:11
a=setup:active
a=mid:0
a=recvonly
a=rtcp-mux
a=rtpmap:96 VP8/90000
m=audio 9 UDP/TLS/RTP/SAVPF 111
c=IN IP4 127.0.0.1
a=rtcp:9 IN IP4 127.0.0.1
a=ice-ufrag:answer
a=ice-pwd:answerpassword
a=fingerprint:sha-256 11:11:11:11:11:11:11:11:11:11:11:11:11:11:11:11:11:11:11:11:11:11:11:11:11:11:11:11:11:11:11:11
a=setup:active
a=mid:1
a=recvonly
a=rtcp-mux
a=rtpmap:111 opus/48000/2`;
}

/**
 * 等待指定时间
 */
export function sleep(ms: number): Promise<void> {
  return new Promise(resolve => setTimeout(resolve, ms));
}

/**
 * 等待条件满足
 */
export async function waitFor(
  condition: () => boolean | Promise<boolean>,
  timeout: number = 5000,
  interval: number = 100
): Promise<void> {
  const startTime = Date.now();
  
  while (Date.now() - startTime < timeout) {
    if (await condition()) {
      return;
    }
    await sleep(interval);
  }
  
  throw new Error(`等待条件超时 (${timeout}ms)`);
}

/**
 * 创建模拟的WebRTC统计信息
 */
export function createMockWebRTCStats(): RTCStatsReport {
  const stats = new Map();
  
  // 添加入站RTP统计
  stats.set('inbound-rtp-video', {
    type: 'inbound-rtp',
    kind: 'video',
    ssrc: 123456,
    packetsReceived: 1000,
    packetsLost: 5,
    bytesReceived: 500000,
    jitter: 0.01,
    framesDecoded: 30,
    framesDropped: 1,
    frameWidth: 1280,
    frameHeight: 720,
    framesPerSecond: 30
  });
  
  stats.set('inbound-rtp-audio', {
    type: 'inbound-rtp',
    kind: 'audio',
    ssrc: 654321,
    packetsReceived: 2000,
    packetsLost: 2,
    bytesReceived: 100000,
    jitter: 0.005
  });
  
  // 添加出站RTP统计
  stats.set('outbound-rtp-video', {
    type: 'outbound-rtp',
    kind: 'video',
    ssrc: 789012,
    packetsSent: 1000,
    bytesSent: 500000,
    framesEncoded: 30,
    keyFramesEncoded: 1,
    frameWidth: 1280,
    frameHeight: 720,
    framesPerSecond: 30
  });
  
  // 添加候选对统计
  stats.set('candidate-pair', {
    type: 'candidate-pair',
    selected: true,
    state: 'succeeded',
    currentRoundTripTime: 0.05,
    availableOutgoingBitrate: 1000000,
    availableIncomingBitrate: 1000000
  });
  
  return stats;
}

/**
 * 模拟HTTP请求
 */
export function createMockRequest(overrides: any = {}): any {
  return {
    method: 'GET',
    url: '/',
    headers: {},
    params: {},
    query: {},
    body: {},
    ip: '127.0.0.1',
    ...overrides
  };
}

/**
 * 模拟HTTP响应
 */
export function createMockResponse(): any {
  const res = {
    status: jest.fn().mockReturnThis(),
    json: jest.fn().mockReturnThis(),
    send: jest.fn().mockReturnThis(),
    end: jest.fn().mockReturnThis(),
    setHeader: jest.fn().mockReturnThis(),
    statusCode: 200,
    headersSent: false
  };
  
  return res;
}

/**
 * 创建测试用的WebSocket连接
 */
export function createMockWebSocket(): any {
  return {
    readyState: 1, // OPEN
    send: jest.fn(),
    close: jest.fn(),
    terminate: jest.fn(),
    addEventListener: jest.fn(),
    removeEventListener: jest.fn(),
    on: jest.fn(),
    off: jest.fn(),
    emit: jest.fn()
  };
}

/**
 * 验证对象结构
 */
export function expectObjectStructure(obj: any, structure: any): void {
  for (const key in structure) {
    expect(obj).toHaveProperty(key);
    
    if (typeof structure[key] === 'object' && structure[key] !== null) {
      if (Array.isArray(structure[key])) {
        expect(Array.isArray(obj[key])).toBe(true);
      } else {
        expectObjectStructure(obj[key], structure[key]);
      }
    } else if (typeof structure[key] === 'string') {
      expect(typeof obj[key]).toBe(structure[key]);
    }
  }
}
