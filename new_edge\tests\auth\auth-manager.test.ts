import { AuthManager } from '../../src/auth/auth-manager';
import { createTestAuthManager } from '../helpers/test-utils';

describe('AuthManager', () => {
  let authManager: AuthManager;
  
  beforeEach(() => {
    authManager = createTestAuthManager();
  });

  describe('用户管理', () => {
    test('应该能够创建用户', async () => {
      const username = 'testuser';
      const password = 'testpassword';
      const permissions = [
        { resource: 'stream', action: 'read', scope: '*' }
      ];
      
      const user = await authManager.createUser(username, password, permissions);
      
      expect(user).toBeDefined();
      expect(user.id).toBeDefined();
      expect(user.username).toBe(username);
      expect(user.password).not.toBe(password); // 应该被加密
      expect(user.permissions).toEqual(permissions);
      expect(typeof user.createdAt).toBe('number');
    });

    test('不应该允许创建重复用户名', async () => {
      const username = 'duplicate';
      const password = 'password';
      
      await authManager.createUser(username, password, []);
      
      await expect(
        authManager.createUser(username, password, [])
      ).rejects.toThrow(`用户 ${username} 已存在`);
    });

    test('应该能够获取用户信息', async () => {
      const username = 'getuser';
      const password = 'password';
      
      const createdUser = await authManager.createUser(username, password, []);
      const retrievedUser = authManager.getUser(createdUser.id);
      
      expect(retrievedUser).toBeDefined();
      expect(retrievedUser!.id).toBe(createdUser.id);
      expect(retrievedUser!.username).toBe(username);
    });

    test('应该能够获取所有用户', async () => {
      const usernames = ['user1', 'user2', 'user3'];
      
      for (const username of usernames) {
        await authManager.createUser(username, 'password', []);
      }
      
      const users = authManager.getAllUsers();
      
      // 包括默认管理员用户
      expect(users.length).toBeGreaterThanOrEqual(usernames.length);
      
      const createdUsernames = users.map(u => u.username);
      usernames.forEach(username => {
        expect(createdUsernames).toContain(username);
      });
    });

    test('应该能够删除用户', async () => {
      const username = 'deleteuser';
      const user = await authManager.createUser(username, 'password', []);
      
      const deleted = authManager.deleteUser(user.id);
      
      expect(deleted).toBe(true);
      expect(authManager.getUser(user.id)).toBeUndefined();
    });

    test('删除不存在的用户应该返回false', () => {
      const deleted = authManager.deleteUser('non-existent-user');
      expect(deleted).toBe(false);
    });
  });

  describe('用户登录', () => {
    test('应该能够成功登录', async () => {
      const username = 'loginuser';
      const password = 'loginpassword';
      
      await authManager.createUser(username, password, []);
      
      const token = await authManager.login(username, password);
      
      expect(token).toBeDefined();
      expect(typeof token).toBe('string');
      expect(token.length).toBeGreaterThan(0);
    });

    test('错误的用户名应该登录失败', async () => {
      await expect(
        authManager.login('nonexistent', 'password')
      ).rejects.toThrow('用户名或密码错误');
    });

    test('错误的密码应该登录失败', async () => {
      const username = 'wrongpassuser';
      await authManager.createUser(username, 'correctpassword', []);
      
      await expect(
        authManager.login(username, 'wrongpassword')
      ).rejects.toThrow('用户名或密码错误');
    });

    test('登录应该更新最后登录时间', async () => {
      const username = 'lasttimeuser';
      const password = 'password';
      
      const user = await authManager.createUser(username, password, []);
      expect(user.lastLogin).toBeUndefined();
      
      await authManager.login(username, password);
      
      const updatedUser = authManager.getUser(user.id);
      expect(updatedUser!.lastLogin).toBeDefined();
      expect(typeof updatedUser!.lastLogin).toBe('number');
    });
  });

  describe('Token验证', () => {
    test('应该能够验证有效token', async () => {
      const username = 'tokenuser';
      const password = 'password';
      const permissions = [
        { resource: 'stream', action: 'read' }
      ];
      
      await authManager.createUser(username, password, permissions);
      const token = await authManager.login(username, password);
      
      const payload = authManager.verifyToken(token);
      
      expect(payload).toBeDefined();
      expect(payload!.username).toBe(username);
      expect(payload!.permissions).toEqual(permissions);
      expect(typeof payload!.iat).toBe('number');
      expect(typeof payload!.exp).toBe('number');
    });

    test('应该拒绝无效token', () => {
      const payload = authManager.verifyToken('invalid-token');
      expect(payload).toBeNull();
    });

    test('应该拒绝过期token', async () => {
      // 创建一个很短过期时间的认证管理器
      const shortAuthManager = new AuthManager({
        enabled: true,
        secret: 'test-secret',
        tokenExpiry: 1, // 1秒过期
        bcryptRounds: 4
      });
      
      const username = 'expireuser';
      const password = 'password';
      
      await shortAuthManager.createUser(username, password, []);
      const token = await shortAuthManager.login(username, password);
      
      // 等待token过期
      await new Promise(resolve => setTimeout(resolve, 1100));
      
      const payload = shortAuthManager.verifyToken(token);
      expect(payload).toBeNull();
    });

    test('应该支持静态token', () => {
      const staticToken = 'static-test-token';
      authManager.addStaticToken(staticToken);
      
      const payload = authManager.verifyToken(staticToken);
      
      expect(payload).toBeDefined();
      expect(payload!.userId).toBe('static');
      expect(payload!.username).toBe('static');
      expect(payload!.permissions).toEqual([{ resource: '*', action: '*' }]);
    });
  });

  describe('权限检查', () => {
    test('应该允许匹配的权限', async () => {
      const username = 'permuser';
      const permissions = [
        { resource: 'stream', action: 'read', scope: 'test-stream' }
      ];
      
      await authManager.createUser(username, 'password', permissions);
      const token = await authManager.login(username, 'password');
      const payload = authManager.verifyToken(token)!;
      
      const hasPermission = authManager.checkPermission(
        payload,
        'stream',
        'read',
        'test-stream'
      );
      
      expect(hasPermission).toBe(true);
    });

    test('应该拒绝不匹配的权限', async () => {
      const username = 'nopermuser';
      const permissions = [
        { resource: 'stream', action: 'read', scope: 'allowed-stream' }
      ];
      
      await authManager.createUser(username, 'password', permissions);
      const token = await authManager.login(username, 'password');
      const payload = authManager.verifyToken(token)!;
      
      const hasPermission = authManager.checkPermission(
        payload,
        'stream',
        'write',
        'allowed-stream'
      );
      
      expect(hasPermission).toBe(false);
    });

    test('应该支持通配符权限', async () => {
      const username = 'wildcarduser';
      const permissions = [
        { resource: '*', action: '*' }
      ];
      
      await authManager.createUser(username, 'password', permissions);
      const token = await authManager.login(username, 'password');
      const payload = authManager.verifyToken(token)!;
      
      const hasPermission1 = authManager.checkPermission(
        payload,
        'stream',
        'read',
        'any-stream'
      );
      
      const hasPermission2 = authManager.checkPermission(
        payload,
        'admin',
        'write'
      );
      
      expect(hasPermission1).toBe(true);
      expect(hasPermission2).toBe(true);
    });

    test('应该支持资源通配符', async () => {
      const username = 'resourcewildcard';
      const permissions = [
        { resource: '*', action: 'read' }
      ];
      
      await authManager.createUser(username, 'password', permissions);
      const token = await authManager.login(username, 'password');
      const payload = authManager.verifyToken(token)!;
      
      const hasReadPermission = authManager.checkPermission(
        payload,
        'stream',
        'read'
      );
      
      const hasWritePermission = authManager.checkPermission(
        payload,
        'stream',
        'write'
      );
      
      expect(hasReadPermission).toBe(true);
      expect(hasWritePermission).toBe(false);
    });

    test('应该支持作用域通配符', async () => {
      const username = 'scopewildcard';
      const permissions = [
        { resource: 'stream', action: 'read', scope: '*' }
      ];
      
      await authManager.createUser(username, 'password', permissions);
      const token = await authManager.login(username, 'password');
      const payload = authManager.verifyToken(token)!;
      
      const hasPermission1 = authManager.checkPermission(
        payload,
        'stream',
        'read',
        'stream-1'
      );
      
      const hasPermission2 = authManager.checkPermission(
        payload,
        'stream',
        'read',
        'stream-2'
      );
      
      expect(hasPermission1).toBe(true);
      expect(hasPermission2).toBe(true);
    });
  });

  describe('认证禁用模式', () => {
    test('认证禁用时应该返回管理员权限', () => {
      const disabledAuthManager = new AuthManager({
        enabled: false,
        secret: 'test-secret',
        tokenExpiry: 3600,
        bcryptRounds: 4
      });
      
      const payload = disabledAuthManager.verifyToken('any-token');
      
      expect(payload).toBeDefined();
      expect(payload!.userId).toBe('admin');
      expect(payload!.username).toBe('admin');
      expect(payload!.permissions).toEqual([{ resource: '*', action: '*' }]);
    });
  });
});
