import { StreamManager } from '../../src/stream/manager';
import { createTestClientInfo, createMockSdpOffer, createMockSdpAnswer, waitFor } from '../helpers/test-utils';

describe('StreamManager', () => {
  let streamManager: StreamManager;
  
  beforeEach(() => {
    streamManager = new StreamManager([{ urls: 'stun:stun.l.google.com:19302' }]);
  });
  
  afterEach(() => {
    // 清理所有流
    const streams = streamManager.getAllStreams();
    streams.forEach(stream => {
      streamManager.deleteStream(stream.id);
    });
  });

  describe('流管理', () => {
    test('应该能够创建流', () => {
      const streamId = 'test-stream-1';
      const metadata = { title: '测试流' };
      
      const stream = streamManager.createStream(streamId, metadata);
      
      expect(stream).toBeDefined();
      expect(stream.id).toBe(streamId);
      expect(stream.metadata).toEqual(metadata);
      expect(stream.publish.active).toBe(false);
      expect(stream.subscribers.count).toBe(0);
      expect(typeof stream.created).toBe('number');
    });

    test('应该能够获取流', () => {
      const streamId = 'test-stream-2';
      streamManager.createStream(streamId);
      
      const stream = streamManager.getStream(streamId);
      
      expect(stream).toBeDefined();
      expect(stream!.id).toBe(streamId);
    });

    test('应该能够获取所有流', () => {
      const streamIds = ['stream-1', 'stream-2', 'stream-3'];
      streamIds.forEach(id => streamManager.createStream(id));
      
      const streams = streamManager.getAllStreams();
      
      expect(streams).toHaveLength(3);
      expect(streams.map(s => s.id).sort()).toEqual(streamIds.sort());
    });

    test('应该能够删除流', () => {
      const streamId = 'test-stream-delete';
      streamManager.createStream(streamId);
      
      const deleted = streamManager.deleteStream(streamId);
      
      expect(deleted).toBe(true);
      expect(streamManager.getStream(streamId)).toBeUndefined();
    });

    test('删除不存在的流应该返回false', () => {
      const deleted = streamManager.deleteStream('non-existent-stream');
      expect(deleted).toBe(false);
    });

    test('创建重复的流应该抛出错误', () => {
      const streamId = 'duplicate-stream';
      streamManager.createStream(streamId);
      
      expect(() => {
        streamManager.createStream(streamId);
      }).toThrow('流 duplicate-stream 已存在');
    });
  });

  describe('发布者管理', () => {
    test('应该能够处理发布请求', async () => {
      const streamId = 'publish-test-stream';
      const clientInfo = createTestClientInfo({ type: 'publisher' });
      const offer = createMockSdpOffer();
      
      const result = await streamManager.handlePublish(streamId, clientInfo, offer);
      
      expect(result).toBeDefined();
      expect(result.sessionId).toBeDefined();
      expect(result.answer).toBeDefined();
      expect(typeof result.sessionId).toBe('string');
      expect(typeof result.answer).toBe('string');
      
      // 验证流状态更新
      const stream = streamManager.getStream(streamId);
      expect(stream).toBeDefined();
      expect(stream!.publish.active).toBe(true);
      expect(stream!.publish.clientId).toBe(clientInfo.id);
    });

    test('同一流不应该允许多个发布者', async () => {
      const streamId = 'single-publisher-stream';
      const clientInfo1 = createTestClientInfo({ type: 'publisher' });
      const clientInfo2 = createTestClientInfo({ type: 'publisher' });
      const offer = createMockSdpOffer();
      
      // 第一个发布者应该成功
      await streamManager.handlePublish(streamId, clientInfo1, offer);
      
      // 第二个发布者应该失败
      await expect(
        streamManager.handlePublish(streamId, clientInfo2, offer)
      ).rejects.toThrow('流 single-publisher-stream 已有发布者');
    });

    test('应该能够移除发布者会话', async () => {
      const streamId = 'remove-publisher-stream';
      const clientInfo = createTestClientInfo({ type: 'publisher' });
      const offer = createMockSdpOffer();
      
      const result = await streamManager.handlePublish(streamId, clientInfo, offer);
      const sessionId = result.sessionId;
      
      // 移除会话
      const removed = streamManager.removeSession(sessionId);
      
      expect(removed).toBe(true);
      
      // 验证流状态更新
      const stream = streamManager.getStream(streamId);
      expect(stream!.publish.active).toBe(false);
      expect(stream!.publish.clientId).toBeUndefined();
    });
  });

  describe('订阅者管理', () => {
    test('应该能够处理订阅请求', async () => {
      const streamId = 'subscribe-test-stream';
      
      // 先创建发布者
      const publisherInfo = createTestClientInfo({ type: 'publisher' });
      await streamManager.handlePublish(streamId, publisherInfo, createMockSdpOffer());
      
      // 然后创建订阅者
      const subscriberInfo = createTestClientInfo({ type: 'subscriber' });
      const result = await streamManager.handleSubscribe(streamId, subscriberInfo, createMockSdpOffer());
      
      expect(result).toBeDefined();
      expect(result.sessionId).toBeDefined();
      expect(result.answer).toBeDefined();
      
      // 验证流状态更新
      const stream = streamManager.getStream(streamId);
      expect(stream!.subscribers.count).toBe(1);
      expect(stream!.subscribers.clients).toContainEqual(subscriberInfo);
    });

    test('没有发布者的流不应该允许订阅', async () => {
      const streamId = 'no-publisher-stream';
      const subscriberInfo = createTestClientInfo({ type: 'subscriber' });
      
      await expect(
        streamManager.handleSubscribe(streamId, subscriberInfo, createMockSdpOffer())
      ).rejects.toThrow('流 no-publisher-stream 没有活跃的发布者');
    });

    test('应该能够移除订阅者会话', async () => {
      const streamId = 'remove-subscriber-stream';
      
      // 创建发布者
      const publisherInfo = createTestClientInfo({ type: 'publisher' });
      await streamManager.handlePublish(streamId, publisherInfo, createMockSdpOffer());
      
      // 创建订阅者
      const subscriberInfo = createTestClientInfo({ type: 'subscriber' });
      const result = await streamManager.handleSubscribe(streamId, subscriberInfo, createMockSdpOffer());
      
      // 移除订阅者会话
      const removed = streamManager.removeSession(result.sessionId);
      
      expect(removed).toBe(true);
      
      // 验证流状态更新
      const stream = streamManager.getStream(streamId);
      expect(stream!.subscribers.count).toBe(0);
      expect(stream!.subscribers.clients).toHaveLength(0);
    });

    test('应该支持多个订阅者', async () => {
      const streamId = 'multi-subscriber-stream';
      
      // 创建发布者
      const publisherInfo = createTestClientInfo({ type: 'publisher' });
      await streamManager.handlePublish(streamId, publisherInfo, createMockSdpOffer());
      
      // 创建多个订阅者
      const subscriberCount = 3;
      const subscriberResults = [];
      
      for (let i = 0; i < subscriberCount; i++) {
        const subscriberInfo = createTestClientInfo({ 
          type: 'subscriber',
          id: `subscriber-${i}`
        });
        const result = await streamManager.handleSubscribe(streamId, subscriberInfo, createMockSdpOffer());
        subscriberResults.push(result);
      }
      
      // 验证所有订阅者都成功创建
      expect(subscriberResults).toHaveLength(subscriberCount);
      subscriberResults.forEach(result => {
        expect(result.sessionId).toBeDefined();
        expect(result.answer).toBeDefined();
      });
      
      // 验证流状态
      const stream = streamManager.getStream(streamId);
      expect(stream!.subscribers.count).toBe(subscriberCount);
      expect(stream!.subscribers.clients).toHaveLength(subscriberCount);
    });
  });

  describe('会话管理', () => {
    test('应该能够获取会话信息', async () => {
      const streamId = 'session-info-stream';
      const clientInfo = createTestClientInfo({ type: 'publisher' });
      
      const result = await streamManager.handlePublish(streamId, clientInfo, createMockSdpOffer());
      const session = streamManager.getSession(result.sessionId);
      
      expect(session).toBeDefined();
      expect(session!.id).toBe(result.sessionId);
      expect(session!.streamId).toBe(streamId);
      expect(session!.clientInfo).toEqual(clientInfo);
      expect(session!.type).toBe('publisher');
    });

    test('应该能够获取流的所有会话', async () => {
      const streamId = 'stream-sessions-test';
      
      // 创建发布者
      const publisherInfo = createTestClientInfo({ type: 'publisher' });
      const publisherResult = await streamManager.handlePublish(streamId, publisherInfo, createMockSdpOffer());
      
      // 创建订阅者
      const subscriberInfo = createTestClientInfo({ type: 'subscriber' });
      const subscriberResult = await streamManager.handleSubscribe(streamId, subscriberInfo, createMockSdpOffer());
      
      // 获取所有会话
      const sessions = streamManager.getStreamSessions(streamId);
      
      expect(sessions).toHaveLength(2);
      expect(sessions.map(s => s.id).sort()).toEqual([
        publisherResult.sessionId,
        subscriberResult.sessionId
      ].sort());
    });

    test('获取不存在会话应该返回undefined', () => {
      const session = streamManager.getSession('non-existent-session');
      expect(session).toBeUndefined();
    });

    test('获取不存在流的会话应该返回空数组', () => {
      const sessions = streamManager.getStreamSessions('non-existent-stream');
      expect(sessions).toEqual([]);
    });
  });
});
