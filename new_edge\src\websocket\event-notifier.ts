import { getLogger } from '../utils/logger';
import { SignalingServer, MessageType } from './signaling-server';
import { StreamManager } from '../stream/manager';
import { mediaQualityMonitor, QualityEventType } from '../webrtc/quality-monitor';
import { webrtcStatsCollector } from '../webrtc/stats-collector';

const logger = getLogger();

/**
 * 事件类型
 */
export enum EventType {
  // 流事件
  STREAM_CREATED = 'stream_created',
  STREAM_DELETED = 'stream_deleted',
  STREAM_UPDATED = 'stream_updated',
  
  // 会话事件
  SESSION_CREATED = 'session_created',
  SESSION_DELETED = 'session_deleted',
  SESSION_UPDATED = 'session_updated',
  
  // 发布者事件
  PUBLISHER_CONNECTED = 'publisher_connected',
  PUBLISHER_DISCONNECTED = 'publisher_disconnected',
  
  // 订阅者事件
  SUBSCRIBER_CONNECTED = 'subscriber_connected',
  SUBSCRIBER_DISCONNECTED = 'subscriber_disconnected',
  
  // 质量事件
  QUALITY_CHANGED = 'quality_changed',
  NETWORK_ISSUE = 'network_issue',
  MEDIA_ISSUE = 'media_issue',
  
  // 系统事件
  SERVER_STATUS = 'server_status',
  RESOURCE_USAGE = 'resource_usage',
  
  // 错误事件
  ERROR_OCCURRED = 'error_occurred'
}

/**
 * 事件数据
 */
export interface EventData {
  type: EventType;
  streamId?: string;
  sessionId?: string;
  clientId?: string;
  data: any;
  timestamp: number;
}

/**
 * 事件过滤器
 */
export interface EventFilter {
  types?: EventType[];
  streamIds?: string[];
  clientIds?: string[];
  minLevel?: 'info' | 'warning' | 'error';
}

/**
 * 事件订阅
 */
export interface EventSubscription {
  id: string;
  clientId: string;
  filter: EventFilter;
  createdAt: number;
}

/**
 * 事件通知器
 */
export class EventNotifier {
  private subscriptions = new Map<string, EventSubscription>();
  private eventHistory: EventData[] = [];
  private maxHistorySize = 1000;
  private statsInterval?: NodeJS.Timeout;

  constructor(
    private signalingServer: SignalingServer,
    private streamManager: StreamManager
  ) {
    this.setupEventListeners();
    this.startStatsCollection();
    
    logger.info('事件通知器已初始化');
  }

  /**
   * 设置事件监听器
   */
  private setupEventListeners(): void {
    // 监听质量事件
    mediaQualityMonitor.addEventListener(QualityEventType.QUALITY_DEGRADED, (event) => {
      this.notifyEvent({
        type: EventType.QUALITY_CHANGED,
        streamId: event.connectionId,
        data: {
          mediaType: event.mediaType,
          previousQuality: event.previousQuality,
          currentQuality: event.currentQuality,
          severity: 'warning'
        },
        timestamp: event.timestamp
      });
    });

    mediaQualityMonitor.addEventListener(QualityEventType.QUALITY_IMPROVED, (event) => {
      this.notifyEvent({
        type: EventType.QUALITY_CHANGED,
        streamId: event.connectionId,
        data: {
          mediaType: event.mediaType,
          previousQuality: event.previousQuality,
          currentQuality: event.currentQuality,
          severity: 'info'
        },
        timestamp: event.timestamp
      });
    });

    mediaQualityMonitor.addEventListener(QualityEventType.NETWORK_ISSUE, (event) => {
      this.notifyEvent({
        type: EventType.NETWORK_ISSUE,
        streamId: event.connectionId,
        data: event.details,
        timestamp: event.timestamp
      });
    });

    mediaQualityMonitor.addEventListener(QualityEventType.MEDIA_ISSUE, (event) => {
      this.notifyEvent({
        type: EventType.MEDIA_ISSUE,
        streamId: event.connectionId,
        data: event.details,
        timestamp: event.timestamp
      });
    });
  }

  /**
   * 订阅事件
   */
  subscribeToEvents(clientId: string, filter: EventFilter): string {
    const subscriptionId = `sub_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    const subscription: EventSubscription = {
      id: subscriptionId,
      clientId,
      filter,
      createdAt: Date.now()
    };
    
    this.subscriptions.set(subscriptionId, subscription);
    
    logger.debug(`客户端订阅事件: ${clientId}, 过滤器: ${JSON.stringify(filter)}`);
    return subscriptionId;
  }

  /**
   * 取消订阅事件
   */
  unsubscribeFromEvents(subscriptionId: string): boolean {
    const success = this.subscriptions.delete(subscriptionId);
    if (success) {
      logger.debug(`取消事件订阅: ${subscriptionId}`);
    }
    return success;
  }

  /**
   * 取消客户端的所有订阅
   */
  unsubscribeClient(clientId: string): void {
    const toRemove: string[] = [];
    
    for (const [subscriptionId, subscription] of this.subscriptions.entries()) {
      if (subscription.clientId === clientId) {
        toRemove.push(subscriptionId);
      }
    }
    
    toRemove.forEach(id => this.subscriptions.delete(id));
    logger.debug(`取消客户端所有订阅: ${clientId}, 数量: ${toRemove.length}`);
  }

  /**
   * 通知事件
   */
  notifyEvent(eventData: EventData): void {
    // 添加到历史记录
    this.eventHistory.push(eventData);
    
    // 限制历史记录大小
    if (this.eventHistory.length > this.maxHistorySize) {
      this.eventHistory.shift();
    }

    // 发送给订阅的客户端
    for (const subscription of this.subscriptions.values()) {
      if (this.matchesFilter(eventData, subscription.filter)) {
        this.sendEventToClient(subscription.clientId, eventData);
      }
    }

    logger.debug(`事件通知: ${eventData.type}, 流: ${eventData.streamId || 'N/A'}`);
  }

  /**
   * 通知流创建
   */
  notifyStreamCreated(streamId: string, streamData: any): void {
    this.notifyEvent({
      type: EventType.STREAM_CREATED,
      streamId,
      data: streamData,
      timestamp: Date.now()
    });
  }

  /**
   * 通知流删除
   */
  notifyStreamDeleted(streamId: string): void {
    this.notifyEvent({
      type: EventType.STREAM_DELETED,
      streamId,
      data: {},
      timestamp: Date.now()
    });
  }

  /**
   * 通知会话创建
   */
  notifySessionCreated(streamId: string, sessionId: string, sessionData: any): void {
    this.notifyEvent({
      type: EventType.SESSION_CREATED,
      streamId,
      sessionId,
      data: sessionData,
      timestamp: Date.now()
    });
  }

  /**
   * 通知会话删除
   */
  notifySessionDeleted(streamId: string, sessionId: string): void {
    this.notifyEvent({
      type: EventType.SESSION_DELETED,
      streamId,
      sessionId,
      data: {},
      timestamp: Date.now()
    });
  }

  /**
   * 通知发布者连接
   */
  notifyPublisherConnected(streamId: string, clientId: string, clientData: any): void {
    this.notifyEvent({
      type: EventType.PUBLISHER_CONNECTED,
      streamId,
      clientId,
      data: clientData,
      timestamp: Date.now()
    });
  }

  /**
   * 通知发布者断开
   */
  notifyPublisherDisconnected(streamId: string, clientId: string): void {
    this.notifyEvent({
      type: EventType.PUBLISHER_DISCONNECTED,
      streamId,
      clientId,
      data: {},
      timestamp: Date.now()
    });
  }

  /**
   * 通知订阅者连接
   */
  notifySubscriberConnected(streamId: string, clientId: string, clientData: any): void {
    this.notifyEvent({
      type: EventType.SUBSCRIBER_CONNECTED,
      streamId,
      clientId,
      data: clientData,
      timestamp: Date.now()
    });
  }

  /**
   * 通知订阅者断开
   */
  notifySubscriberDisconnected(streamId: string, clientId: string): void {
    this.notifyEvent({
      type: EventType.SUBSCRIBER_DISCONNECTED,
      streamId,
      clientId,
      data: {},
      timestamp: Date.now()
    });
  }

  /**
   * 通知错误
   */
  notifyError(error: string, streamId?: string, sessionId?: string): void {
    this.notifyEvent({
      type: EventType.ERROR_OCCURRED,
      streamId,
      sessionId,
      data: { error },
      timestamp: Date.now()
    });
  }

  /**
   * 检查事件是否匹配过滤器
   */
  private matchesFilter(eventData: EventData, filter: EventFilter): boolean {
    // 检查事件类型
    if (filter.types && !filter.types.includes(eventData.type)) {
      return false;
    }

    // 检查流ID
    if (filter.streamIds && eventData.streamId && !filter.streamIds.includes(eventData.streamId)) {
      return false;
    }

    // 检查客户端ID
    if (filter.clientIds && eventData.clientId && !filter.clientIds.includes(eventData.clientId)) {
      return false;
    }

    // 检查最小级别
    if (filter.minLevel) {
      const eventLevel = this.getEventLevel(eventData);
      const minLevel = filter.minLevel;
      
      const levels = ['info', 'warning', 'error'];
      const eventLevelIndex = levels.indexOf(eventLevel);
      const minLevelIndex = levels.indexOf(minLevel);
      
      if (eventLevelIndex < minLevelIndex) {
        return false;
      }
    }

    return true;
  }

  /**
   * 获取事件级别
   */
  private getEventLevel(eventData: EventData): 'info' | 'warning' | 'error' {
    switch (eventData.type) {
      case EventType.ERROR_OCCURRED:
      case EventType.NETWORK_ISSUE:
      case EventType.MEDIA_ISSUE:
        return 'error';
      
      case EventType.QUALITY_CHANGED:
        return eventData.data?.severity === 'warning' ? 'warning' : 'info';
      
      default:
        return 'info';
    }
  }

  /**
   * 发送事件给客户端
   */
  private sendEventToClient(clientId: string, eventData: EventData): void {
    this.signalingServer.broadcast({
      type: MessageType.QUALITY_UPDATE,
      data: eventData,
      timestamp: Date.now()
    }, clientId);
  }

  /**
   * 获取事件历史
   */
  getEventHistory(filter?: EventFilter): EventData[] {
    if (!filter) {
      return [...this.eventHistory];
    }

    return this.eventHistory.filter(event => this.matchesFilter(event, filter));
  }

  /**
   * 获取订阅统计
   */
  getSubscriptionStats() {
    const totalSubscriptions = this.subscriptions.size;
    const clientCounts = new Map<string, number>();
    
    for (const subscription of this.subscriptions.values()) {
      const count = clientCounts.get(subscription.clientId) || 0;
      clientCounts.set(subscription.clientId, count + 1);
    }

    return {
      totalSubscriptions,
      uniqueClients: clientCounts.size,
      clientSubscriptions: Array.from(clientCounts.entries()).map(([clientId, count]) => ({
        clientId,
        subscriptionCount: count
      }))
    };
  }

  /**
   * 启动统计收集
   */
  private startStatsCollection(): void {
    this.statsInterval = setInterval(() => {
      this.collectAndNotifyStats();
    }, 30000); // 每30秒收集一次统计信息
  }

  /**
   * 收集并通知统计信息
   */
  private collectAndNotifyStats(): void {
    // 收集服务器状态
    const serverStats = this.signalingServer.getStats();
    const streamStats = this.streamManager.getAllStreams();
    
    this.notifyEvent({
      type: EventType.SERVER_STATUS,
      data: {
        websocket: serverStats,
        streams: {
          total: streamStats.length,
          active: streamStats.filter(s => s.publish.active).length
        },
        timestamp: Date.now()
      },
      timestamp: Date.now()
    });

    // 收集资源使用情况
    const memoryUsage = process.memoryUsage();
    this.notifyEvent({
      type: EventType.RESOURCE_USAGE,
      data: {
        memory: {
          rss: Math.round(memoryUsage.rss / 1024 / 1024),
          heapUsed: Math.round(memoryUsage.heapUsed / 1024 / 1024),
          heapTotal: Math.round(memoryUsage.heapTotal / 1024 / 1024),
          external: Math.round(memoryUsage.external / 1024 / 1024)
        },
        uptime: process.uptime(),
        timestamp: Date.now()
      },
      timestamp: Date.now()
    });
  }

  /**
   * 停止事件通知器
   */
  stop(): void {
    if (this.statsInterval) {
      clearInterval(this.statsInterval);
    }

    this.subscriptions.clear();
    this.eventHistory = [];
    
    logger.info('事件通知器已停止');
  }
}

// 导出事件通知器实例创建函数
export function createEventNotifier(
  signalingServer: SignalingServer,
  streamManager: StreamManager
): EventNotifier {
  return new EventNotifier(signalingServer, streamManager);
}
