import { getLogger } from './logger';

const logger = getLogger();

/**
 * 连接池配置
 */
export interface PoolConfig {
  maxConnections: number;
  minConnections: number;
  acquireTimeout: number;
  idleTimeout: number;
  maxRetries: number;
}

/**
 * 连接包装器
 */
export interface PooledConnection<T> {
  id: string;
  connection: T;
  createdAt: number;
  lastUsed: number;
  inUse: boolean;
  retryCount: number;
}

/**
 * 连接工厂接口
 */
export interface ConnectionFactory<T> {
  create(): Promise<T>;
  validate(connection: T): Promise<boolean>;
  destroy(connection: T): Promise<void>;
}

/**
 * 通用连接池
 */
export class ConnectionPool<T> {
  private connections = new Map<string, PooledConnection<T>>();
  private waitingQueue: Array<{
    resolve: (connection: PooledConnection<T>) => void;
    reject: (error: Error) => void;
    timeout: NodeJS.Timeout;
  }> = [];
  
  private cleanupTimer?: NodeJS.Timeout;
  private isShuttingDown = false;

  constructor(
    private factory: ConnectionFactory<T>,
    private config: PoolConfig
  ) {
    // 启动清理定时器
    this.startCleanupTimer();
    
    // 预创建最小连接数
    this.preCreateConnections();
  }

  /**
   * 获取连接
   */
  async acquire(): Promise<PooledConnection<T>> {
    if (this.isShuttingDown) {
      throw new Error('连接池正在关闭');
    }

    // 尝试获取空闲连接
    const idleConnection = this.getIdleConnection();
    if (idleConnection) {
      idleConnection.inUse = true;
      idleConnection.lastUsed = Date.now();
      return idleConnection;
    }

    // 如果还能创建新连接
    if (this.connections.size < this.config.maxConnections) {
      try {
        const connection = await this.createConnection();
        connection.inUse = true;
        return connection;
      } catch (error) {
        logger.error(`创建连接失败: ${error}`);
      }
    }

    // 等待连接可用
    return this.waitForConnection();
  }

  /**
   * 释放连接
   */
  release(pooledConnection: PooledConnection<T>): void {
    const connection = this.connections.get(pooledConnection.id);
    if (!connection) {
      logger.warn(`尝试释放不存在的连接: ${pooledConnection.id}`);
      return;
    }

    connection.inUse = false;
    connection.lastUsed = Date.now();

    // 处理等待队列
    this.processWaitingQueue();
  }

  /**
   * 移除连接
   */
  async remove(pooledConnection: PooledConnection<T>): Promise<void> {
    const connection = this.connections.get(pooledConnection.id);
    if (!connection) {
      return;
    }

    this.connections.delete(pooledConnection.id);
    
    try {
      await this.factory.destroy(connection.connection);
    } catch (error) {
      logger.error(`销毁连接失败: ${error}`);
    }

    // 处理等待队列
    this.processWaitingQueue();
  }

  /**
   * 获取池状态
   */
  getStats() {
    const total = this.connections.size;
    const inUse = Array.from(this.connections.values()).filter(c => c.inUse).length;
    const idle = total - inUse;
    const waiting = this.waitingQueue.length;

    return {
      total,
      inUse,
      idle,
      waiting,
      maxConnections: this.config.maxConnections,
      minConnections: this.config.minConnections
    };
  }

  /**
   * 关闭连接池
   */
  async shutdown(): Promise<void> {
    this.isShuttingDown = true;

    // 清理定时器
    if (this.cleanupTimer) {
      clearInterval(this.cleanupTimer);
    }

    // 拒绝所有等待的请求
    this.waitingQueue.forEach(({ reject, timeout }) => {
      clearTimeout(timeout);
      reject(new Error('连接池正在关闭'));
    });
    this.waitingQueue = [];

    // 关闭所有连接
    const closePromises = Array.from(this.connections.values()).map(async (pooledConnection) => {
      try {
        await this.factory.destroy(pooledConnection.connection);
      } catch (error) {
        logger.error(`关闭连接失败: ${error}`);
      }
    });

    await Promise.allSettled(closePromises);
    this.connections.clear();

    logger.info('连接池已关闭');
  }

  /**
   * 获取空闲连接
   */
  private getIdleConnection(): PooledConnection<T> | null {
    for (const connection of this.connections.values()) {
      if (!connection.inUse) {
        return connection;
      }
    }
    return null;
  }

  /**
   * 创建新连接
   */
  private async createConnection(): Promise<PooledConnection<T>> {
    const id = `conn_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const connection = await this.factory.create();
    
    const pooledConnection: PooledConnection<T> = {
      id,
      connection,
      createdAt: Date.now(),
      lastUsed: Date.now(),
      inUse: false,
      retryCount: 0
    };

    this.connections.set(id, pooledConnection);
    logger.debug(`创建新连接: ${id}`);
    
    return pooledConnection;
  }

  /**
   * 等待连接可用
   */
  private waitForConnection(): Promise<PooledConnection<T>> {
    return new Promise((resolve, reject) => {
      const timeout = setTimeout(() => {
        // 从等待队列中移除
        const index = this.waitingQueue.findIndex(item => item.resolve === resolve);
        if (index !== -1) {
          this.waitingQueue.splice(index, 1);
        }
        reject(new Error('获取连接超时'));
      }, this.config.acquireTimeout);

      this.waitingQueue.push({ resolve, reject, timeout });
    });
  }

  /**
   * 处理等待队列
   */
  private processWaitingQueue(): void {
    if (this.waitingQueue.length === 0) {
      return;
    }

    const idleConnection = this.getIdleConnection();
    if (idleConnection) {
      const waiter = this.waitingQueue.shift();
      if (waiter) {
        clearTimeout(waiter.timeout);
        idleConnection.inUse = true;
        idleConnection.lastUsed = Date.now();
        waiter.resolve(idleConnection);
      }
    }
  }

  /**
   * 启动清理定时器
   */
  private startCleanupTimer(): void {
    this.cleanupTimer = setInterval(() => {
      this.cleanup();
    }, 30000); // 每30秒清理一次
  }

  /**
   * 清理过期连接
   */
  private async cleanup(): Promise<void> {
    const now = Date.now();
    const toRemove: PooledConnection<T>[] = [];

    for (const connection of this.connections.values()) {
      // 跳过正在使用的连接
      if (connection.inUse) {
        continue;
      }

      // 检查是否过期
      if (now - connection.lastUsed > this.config.idleTimeout) {
        toRemove.push(connection);
      }
      // 验证连接是否仍然有效
      else {
        try {
          const isValid = await this.factory.validate(connection.connection);
          if (!isValid) {
            toRemove.push(connection);
          }
        } catch (error) {
          logger.warn(`连接验证失败: ${error}`);
          toRemove.push(connection);
        }
      }
    }

    // 移除无效连接，但保持最小连接数
    const currentCount = this.connections.size;
    const maxToRemove = Math.max(0, currentCount - this.config.minConnections);
    const actualToRemove = toRemove.slice(0, maxToRemove);

    for (const connection of actualToRemove) {
      await this.remove(connection);
      logger.debug(`清理连接: ${connection.id}`);
    }
  }

  /**
   * 预创建最小连接数
   */
  private async preCreateConnections(): Promise<void> {
    const promises: Promise<void>[] = [];
    
    for (let i = 0; i < this.config.minConnections; i++) {
      promises.push(
        this.createConnection()
          .then(() => {})
          .catch(error => {
            logger.warn(`预创建连接失败: ${error}`);
          })
      );
    }

    await Promise.allSettled(promises);
  }
}
