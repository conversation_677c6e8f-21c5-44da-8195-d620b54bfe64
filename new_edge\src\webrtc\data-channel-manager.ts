import { getLogger } from '../utils/logger';
import { asyncOptimizer, TaskPriority } from '../utils/async-optimizer';

const logger = getLogger();

/**
 * 数据通道配置
 */
export interface DataChannelConfig {
  ordered?: boolean;
  maxPacketLifeTime?: number;
  maxRetransmits?: number;
  protocol?: string;
  negotiated?: boolean;
  id?: number;
}

/**
 * 数据通道状态
 */
export enum DataChannelState {
  CONNECTING = 'connecting',
  OPEN = 'open',
  CLOSING = 'closing',
  CLOSED = 'closed'
}

/**
 * 消息类型
 */
export enum MessageType {
  TEXT = 'text',
  BINARY = 'binary',
  JSON = 'json',
  CUSTOM = 'custom'
}

/**
 * 数据通道消息
 */
export interface DataChannelMessage {
  type: MessageType;
  data: any;
  timestamp: number;
  channelId: string;
  sessionId?: string;
}

/**
 * 数据通道信息
 */
export interface DataChannelInfo {
  id: string;
  label: string;
  state: DataChannelState;
  config: DataChannelConfig;
  sessionId: string;
  createdAt: number;
  lastActivity: number;
  messageCount: number;
  bytesReceived: number;
  bytesSent: number;
}

/**
 * 数据通道事件
 */
export interface DataChannelEvent {
  type: 'open' | 'close' | 'error' | 'message';
  channelId: string;
  sessionId: string;
  data?: any;
  timestamp: number;
}

/**
 * 消息处理器
 */
export type MessageHandler = (message: DataChannelMessage) => Promise<void> | void;

/**
 * 数据通道管理器
 */
export class DataChannelManager {
  private channels = new Map<string, DataChannelInfo>();
  private nativeChannels = new Map<string, RTCDataChannel>();
  private messageHandlers = new Map<string, MessageHandler[]>();
  private eventListeners = new Map<string, Function[]>();
  private messageQueue = new Map<string, DataChannelMessage[]>();
  private maxQueueSize = 1000;
  private heartbeatInterval = 30000; // 30秒心跳间隔
  private heartbeatTimers = new Map<string, NodeJS.Timeout>();

  constructor() {
    // 注册批处理器用于消息处理
    asyncOptimizer.registerBatchProcessor('data-channel-messages', {
      maxBatchSize: 100,
      maxWaitTime: 10,
      processor: this.processBatchMessages.bind(this)
    });

    logger.info('数据通道管理器已初始化');
  }

  /**
   * 创建数据通道
   */
  createDataChannel(
    peerConnection: RTCPeerConnection,
    sessionId: string,
    label: string,
    config: DataChannelConfig = {}
  ): string {
    const channelId = `dc_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    try {
      // 创建原生数据通道
      const dataChannel = peerConnection.createDataChannel(label, config);
      
      // 创建通道信息
      const channelInfo: DataChannelInfo = {
        id: channelId,
        label,
        state: DataChannelState.CONNECTING,
        config,
        sessionId,
        createdAt: Date.now(),
        lastActivity: Date.now(),
        messageCount: 0,
        bytesReceived: 0,
        bytesSent: 0
      };

      this.channels.set(channelId, channelInfo);
      this.nativeChannels.set(channelId, dataChannel);
      this.messageQueue.set(channelId, []);

      // 设置事件监听器
      this.setupDataChannelEvents(channelId, dataChannel);

      // 启动心跳
      this.startHeartbeat(channelId);

      logger.info(`创建数据通道: ${channelId} (${label})`);
      return channelId;
    } catch (error) {
      logger.error(`创建数据通道失败: ${error}`);
      throw error;
    }
  }

  /**
   * 处理接收到的数据通道
   */
  handleIncomingDataChannel(
    dataChannel: RTCDataChannel,
    sessionId: string
  ): string {
    const channelId = `dc_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    // 创建通道信息
    const channelInfo: DataChannelInfo = {
      id: channelId,
      label: dataChannel.label,
      state: dataChannel.readyState as DataChannelState,
      config: {
        ordered: dataChannel.ordered,
        maxPacketLifeTime: dataChannel.maxPacketLifeTime,
        maxRetransmits: dataChannel.maxRetransmits,
        protocol: dataChannel.protocol,
        id: dataChannel.id
      },
      sessionId,
      createdAt: Date.now(),
      lastActivity: Date.now(),
      messageCount: 0,
      bytesReceived: 0,
      bytesSent: 0
    };

    this.channels.set(channelId, channelInfo);
    this.nativeChannels.set(channelId, dataChannel);
    this.messageQueue.set(channelId, []);

    // 设置事件监听器
    this.setupDataChannelEvents(channelId, dataChannel);

    // 启动心跳
    this.startHeartbeat(channelId);

    logger.info(`接收数据通道: ${channelId} (${dataChannel.label})`);
    return channelId;
  }

  /**
   * 发送消息
   */
  async sendMessage(
    channelId: string,
    data: any,
    type: MessageType = MessageType.TEXT
  ): Promise<void> {
    const channel = this.nativeChannels.get(channelId);
    const channelInfo = this.channels.get(channelId);
    
    if (!channel || !channelInfo) {
      throw new Error(`数据通道不存在: ${channelId}`);
    }

    if (channel.readyState !== 'open') {
      throw new Error(`数据通道未打开: ${channelId} (${channel.readyState})`);
    }

    try {
      let messageData: string | ArrayBuffer;
      
      switch (type) {
        case MessageType.TEXT:
          messageData = String(data);
          break;
        case MessageType.JSON:
          messageData = JSON.stringify(data);
          break;
        case MessageType.BINARY:
          messageData = data instanceof ArrayBuffer ? data : new TextEncoder().encode(data).buffer;
          break;
        case MessageType.CUSTOM:
          messageData = data;
          break;
        default:
          messageData = String(data);
      }

      // 发送消息
      channel.send(messageData);

      // 更新统计信息
      channelInfo.lastActivity = Date.now();
      channelInfo.messageCount++;
      channelInfo.bytesSent += this.getMessageSize(messageData);

      logger.debug(`发送数据通道消息: ${channelId}, 类型: ${type}`);
    } catch (error) {
      logger.error(`发送数据通道消息失败: ${channelId}, 错误: ${error}`);
      throw error;
    }
  }

  /**
   * 广播消息给会话的所有数据通道
   */
  async broadcastToSession(
    sessionId: string,
    data: any,
    type: MessageType = MessageType.TEXT,
    excludeChannelId?: string
  ): Promise<void> {
    const sessionChannels = Array.from(this.channels.values())
      .filter(info => info.sessionId === sessionId && info.id !== excludeChannelId);

    const promises = sessionChannels.map(channelInfo =>
      this.sendMessage(channelInfo.id, data, type).catch(error => {
        logger.warn(`广播消息失败: ${channelInfo.id}, 错误: ${error}`);
      })
    );

    await Promise.allSettled(promises);
  }

  /**
   * 注册消息处理器
   */
  registerMessageHandler(channelId: string, handler: MessageHandler): void {
    if (!this.messageHandlers.has(channelId)) {
      this.messageHandlers.set(channelId, []);
    }
    this.messageHandlers.get(channelId)!.push(handler);
  }

  /**
   * 注册全局消息处理器
   */
  registerGlobalMessageHandler(handler: MessageHandler): void {
    this.registerMessageHandler('*', handler);
  }

  /**
   * 移除消息处理器
   */
  removeMessageHandler(channelId: string, handler: MessageHandler): void {
    const handlers = this.messageHandlers.get(channelId);
    if (handlers) {
      const index = handlers.indexOf(handler);
      if (index !== -1) {
        handlers.splice(index, 1);
      }
    }
  }

  /**
   * 关闭数据通道
   */
  closeDataChannel(channelId: string): void {
    const channel = this.nativeChannels.get(channelId);
    const channelInfo = this.channels.get(channelId);
    
    if (channel) {
      channel.close();
    }

    if (channelInfo) {
      channelInfo.state = DataChannelState.CLOSED;
    }

    // 清理心跳定时器
    const heartbeatTimer = this.heartbeatTimers.get(channelId);
    if (heartbeatTimer) {
      clearInterval(heartbeatTimer);
      this.heartbeatTimers.delete(channelId);
    }

    // 清理资源
    this.nativeChannels.delete(channelId);
    this.messageQueue.delete(channelId);
    this.messageHandlers.delete(channelId);

    logger.info(`关闭数据通道: ${channelId}`);
  }

  /**
   * 获取数据通道信息
   */
  getChannelInfo(channelId: string): DataChannelInfo | undefined {
    return this.channels.get(channelId);
  }

  /**
   * 获取会话的所有数据通道
   */
  getSessionChannels(sessionId: string): DataChannelInfo[] {
    return Array.from(this.channels.values())
      .filter(info => info.sessionId === sessionId);
  }

  /**
   * 获取所有数据通道
   */
  getAllChannels(): DataChannelInfo[] {
    return Array.from(this.channels.values());
  }

  /**
   * 设置数据通道事件监听器
   */
  private setupDataChannelEvents(channelId: string, dataChannel: RTCDataChannel): void {
    dataChannel.addEventListener('open', () => {
      const channelInfo = this.channels.get(channelId);
      if (channelInfo) {
        channelInfo.state = DataChannelState.OPEN;
        channelInfo.lastActivity = Date.now();
      }
      
      this.emitEvent({
        type: 'open',
        channelId,
        sessionId: channelInfo?.sessionId || '',
        timestamp: Date.now()
      });
      
      logger.debug(`数据通道打开: ${channelId}`);
    });

    dataChannel.addEventListener('close', () => {
      const channelInfo = this.channels.get(channelId);
      if (channelInfo) {
        channelInfo.state = DataChannelState.CLOSED;
      }
      
      this.emitEvent({
        type: 'close',
        channelId,
        sessionId: channelInfo?.sessionId || '',
        timestamp: Date.now()
      });
      
      this.closeDataChannel(channelId);
      logger.debug(`数据通道关闭: ${channelId}`);
    });

    dataChannel.addEventListener('error', (event) => {
      logger.error(`数据通道错误: ${channelId}, 错误: ${event}`);
      
      this.emitEvent({
        type: 'error',
        channelId,
        sessionId: this.channels.get(channelId)?.sessionId || '',
        data: event,
        timestamp: Date.now()
      });
    });

    dataChannel.addEventListener('message', (event) => {
      this.handleMessage(channelId, event.data);
    });
  }

  /**
   * 处理接收到的消息
   */
  private async handleMessage(channelId: string, data: any): Promise<void> {
    const channelInfo = this.channels.get(channelId);
    if (!channelInfo) {
      return;
    }

    // 更新统计信息
    channelInfo.lastActivity = Date.now();
    channelInfo.messageCount++;
    channelInfo.bytesReceived += this.getMessageSize(data);

    // 确定消息类型
    let messageType: MessageType;
    let messageData: any;

    if (typeof data === 'string') {
      try {
        messageData = JSON.parse(data);
        messageType = MessageType.JSON;
      } catch {
        messageData = data;
        messageType = MessageType.TEXT;
      }
    } else if (data instanceof ArrayBuffer) {
      messageData = data;
      messageType = MessageType.BINARY;
    } else {
      messageData = data;
      messageType = MessageType.CUSTOM;
    }

    const message: DataChannelMessage = {
      type: messageType,
      data: messageData,
      timestamp: Date.now(),
      channelId,
      sessionId: channelInfo.sessionId
    };

    // 添加到批处理队列
    await asyncOptimizer.addToBatch('data-channel-messages', {
      channelId,
      message
    });

    logger.debug(`接收数据通道消息: ${channelId}, 类型: ${messageType}`);
  }

  /**
   * 批处理消息
   */
  private async processBatchMessages(items: Array<{ channelId: string; message: DataChannelMessage }>): Promise<any[]> {
    const results = [];
    
    for (const item of items) {
      try {
        // 调用通道特定的处理器
        const channelHandlers = this.messageHandlers.get(item.channelId) || [];
        for (const handler of channelHandlers) {
          await handler(item.message);
        }
        
        // 调用全局处理器
        const globalHandlers = this.messageHandlers.get('*') || [];
        for (const handler of globalHandlers) {
          await handler(item.message);
        }
        
        // 发出消息事件
        this.emitEvent({
          type: 'message',
          channelId: item.channelId,
          sessionId: item.message.sessionId || '',
          data: item.message,
          timestamp: item.message.timestamp
        });
        
        results.push({ success: true, channelId: item.channelId });
      } catch (error) {
        logger.error(`处理数据通道消息失败: ${item.channelId}, 错误: ${error}`);
        results.push({ success: false, channelId: item.channelId, error });
      }
    }
    
    return results;
  }

  /**
   * 启动心跳
   */
  private startHeartbeat(channelId: string): void {
    const timer = setInterval(() => {
      this.sendHeartbeat(channelId);
    }, this.heartbeatInterval);
    
    this.heartbeatTimers.set(channelId, timer);
  }

  /**
   * 发送心跳
   */
  private async sendHeartbeat(channelId: string): Promise<void> {
    try {
      await this.sendMessage(channelId, {
        type: 'heartbeat',
        timestamp: Date.now()
      }, MessageType.JSON);
    } catch (error) {
      logger.debug(`发送心跳失败: ${channelId}, 错误: ${error}`);
    }
  }

  /**
   * 获取消息大小
   */
  private getMessageSize(data: any): number {
    if (typeof data === 'string') {
      return new TextEncoder().encode(data).length;
    } else if (data instanceof ArrayBuffer) {
      return data.byteLength;
    } else {
      return JSON.stringify(data).length;
    }
  }

  /**
   * 发出事件
   */
  private emitEvent(event: DataChannelEvent): void {
    const listeners = this.eventListeners.get(event.type) || [];
    listeners.forEach(listener => {
      try {
        listener(event);
      } catch (error) {
        logger.error(`数据通道事件监听器错误: ${error}`);
      }
    });
  }

  /**
   * 添加事件监听器
   */
  addEventListener(type: string, listener: Function): void {
    if (!this.eventListeners.has(type)) {
      this.eventListeners.set(type, []);
    }
    this.eventListeners.get(type)!.push(listener);
  }

  /**
   * 移除事件监听器
   */
  removeEventListener(type: string, listener: Function): void {
    const listeners = this.eventListeners.get(type);
    if (listeners) {
      const index = listeners.indexOf(listener);
      if (index !== -1) {
        listeners.splice(index, 1);
      }
    }
  }

  /**
   * 获取统计信息
   */
  getStats() {
    const totalChannels = this.channels.size;
    const openChannels = Array.from(this.channels.values())
      .filter(info => info.state === DataChannelState.OPEN).length;
    
    const totalMessages = Array.from(this.channels.values())
      .reduce((sum, info) => sum + info.messageCount, 0);
    
    const totalBytesReceived = Array.from(this.channels.values())
      .reduce((sum, info) => sum + info.bytesReceived, 0);
    
    const totalBytesSent = Array.from(this.channels.values())
      .reduce((sum, info) => sum + info.bytesSent, 0);

    return {
      totalChannels,
      openChannels,
      totalMessages,
      totalBytesReceived,
      totalBytesSent,
      queuedMessages: Array.from(this.messageQueue.values())
        .reduce((sum, queue) => sum + queue.length, 0)
    };
  }

  /**
   * 停止数据通道管理器
   */
  stop(): void {
    // 关闭所有数据通道
    for (const channelId of this.channels.keys()) {
      this.closeDataChannel(channelId);
    }

    // 清理所有资源
    this.channels.clear();
    this.nativeChannels.clear();
    this.messageHandlers.clear();
    this.eventListeners.clear();
    this.messageQueue.clear();
    this.heartbeatTimers.clear();

    logger.info('数据通道管理器已停止');
  }
}

// 全局数据通道管理器实例
export const dataChannelManager = new DataChannelManager();
