import { getLogger } from './logger';

const logger = getLogger();

/**
 * 内存使用统计
 */
export interface MemoryStats {
  used: number;
  total: number;
  free: number;
  percentage: number;
  heapUsed: number;
  heapTotal: number;
  external: number;
  rss: number;
}

/**
 * 内存管理配置
 */
export interface MemoryConfig {
  maxMemoryUsage: number; // 最大内存使用量 (MB)
  warningThreshold: number; // 警告阈值 (百分比)
  criticalThreshold: number; // 严重阈值 (百分比)
  gcInterval: number; // GC间隔 (毫秒)
  enableAutoGC: boolean; // 启用自动GC
}

/**
 * 内存管理器
 */
export class MemoryManager {
  private config: MemoryConfig;
  private gcTimer?: NodeJS.Timeout;
  private statsTimer?: NodeJS.Timeout;
  private lastGCTime = 0;
  private gcCount = 0;
  private memoryLeakDetection = new Map<string, number>();

  constructor(config: Partial<MemoryConfig> = {}) {
    this.config = {
      maxMemoryUsage: config.maxMemoryUsage || 1024, // 1GB
      warningThreshold: config.warningThreshold || 80,
      criticalThreshold: config.criticalThreshold || 90,
      gcInterval: config.gcInterval || 60000, // 1分钟
      enableAutoGC: config.enableAutoGC !== false
    };

    this.startMonitoring();
  }

  /**
   * 获取内存统计信息
   */
  getMemoryStats(): MemoryStats {
    const memUsage = process.memoryUsage();
    const totalMemory = this.config.maxMemoryUsage * 1024 * 1024; // 转换为字节
    const usedMemory = memUsage.rss;
    const freeMemory = totalMemory - usedMemory;
    const percentage = (usedMemory / totalMemory) * 100;

    return {
      used: Math.round(usedMemory / 1024 / 1024), // MB
      total: Math.round(totalMemory / 1024 / 1024), // MB
      free: Math.round(freeMemory / 1024 / 1024), // MB
      percentage: Math.round(percentage * 100) / 100,
      heapUsed: Math.round(memUsage.heapUsed / 1024 / 1024), // MB
      heapTotal: Math.round(memUsage.heapTotal / 1024 / 1024), // MB
      external: Math.round(memUsage.external / 1024 / 1024), // MB
      rss: Math.round(memUsage.rss / 1024 / 1024) // MB
    };
  }

  /**
   * 检查内存使用情况
   */
  checkMemoryUsage(): { status: 'normal' | 'warning' | 'critical', stats: MemoryStats } {
    const stats = this.getMemoryStats();
    let status: 'normal' | 'warning' | 'critical' = 'normal';

    if (stats.percentage >= this.config.criticalThreshold) {
      status = 'critical';
      logger.error(`内存使用严重: ${stats.percentage}% (${stats.used}MB/${stats.total}MB)`);
    } else if (stats.percentage >= this.config.warningThreshold) {
      status = 'warning';
      logger.warn(`内存使用警告: ${stats.percentage}% (${stats.used}MB/${stats.total}MB)`);
    }

    return { status, stats };
  }

  /**
   * 强制垃圾回收
   */
  forceGC(): boolean {
    if (global.gc) {
      const before = process.memoryUsage();
      global.gc();
      const after = process.memoryUsage();
      
      this.lastGCTime = Date.now();
      this.gcCount++;
      
      const freed = before.heapUsed - after.heapUsed;
      logger.info(`手动GC完成，释放内存: ${Math.round(freed / 1024 / 1024)}MB`);
      return true;
    } else {
      logger.warn('GC不可用，请使用 --expose-gc 启动参数');
      return false;
    }
  }

  /**
   * 自动垃圾回收
   */
  private autoGC(): void {
    if (!this.config.enableAutoGC) {
      return;
    }

    const { status } = this.checkMemoryUsage();
    
    if (status === 'critical') {
      logger.info('内存使用严重，执行强制GC');
      this.forceGC();
    } else if (status === 'warning') {
      // 检查是否距离上次GC超过一定时间
      const timeSinceLastGC = Date.now() - this.lastGCTime;
      if (timeSinceLastGC > this.config.gcInterval * 2) {
        logger.info('内存使用警告，执行预防性GC');
        this.forceGC();
      }
    }
  }

  /**
   * 注册内存使用跟踪
   */
  trackMemoryUsage(name: string): void {
    const stats = this.getMemoryStats();
    this.memoryLeakDetection.set(name, stats.heapUsed);
  }

  /**
   * 检查内存泄漏
   */
  checkMemoryLeak(name: string, threshold: number = 50): boolean {
    const currentUsage = this.getMemoryStats().heapUsed;
    const initialUsage = this.memoryLeakDetection.get(name);
    
    if (initialUsage === undefined) {
      logger.warn(`未找到内存跟踪记录: ${name}`);
      return false;
    }

    const increase = currentUsage - initialUsage;
    const increasePercentage = (increase / initialUsage) * 100;

    if (increasePercentage > threshold) {
      logger.warn(`检测到可能的内存泄漏: ${name}, 增长 ${Math.round(increase)}MB (${Math.round(increasePercentage)}%)`);
      return true;
    }

    return false;
  }

  /**
   * 清理内存跟踪
   */
  clearMemoryTracking(name?: string): void {
    if (name) {
      this.memoryLeakDetection.delete(name);
    } else {
      this.memoryLeakDetection.clear();
    }
  }

  /**
   * 获取GC统计信息
   */
  getGCStats() {
    return {
      count: this.gcCount,
      lastGCTime: this.lastGCTime,
      timeSinceLastGC: Date.now() - this.lastGCTime,
      isGCAvailable: !!global.gc
    };
  }

  /**
   * 启动内存监控
   */
  private startMonitoring(): void {
    // 定期检查内存使用情况
    this.statsTimer = setInterval(() => {
      this.autoGC();
    }, this.config.gcInterval);

    // 监听进程退出事件
    process.on('exit', () => {
      this.stop();
    });

    // 监听内存不足事件
    process.on('warning', (warning) => {
      if (warning.name === 'MaxListenersExceededWarning') {
        logger.warn('检测到可能的内存泄漏: 事件监听器过多');
      }
    });

    logger.info('内存管理器已启动');
  }

  /**
   * 停止内存监控
   */
  stop(): void {
    if (this.gcTimer) {
      clearInterval(this.gcTimer);
      this.gcTimer = undefined;
    }

    if (this.statsTimer) {
      clearInterval(this.statsTimer);
      this.statsTimer = undefined;
    }

    this.memoryLeakDetection.clear();
    logger.info('内存管理器已停止');
  }

  /**
   * 创建内存快照
   */
  createSnapshot(name: string): MemorySnapshot {
    const stats = this.getMemoryStats();
    const gcStats = this.getGCStats();
    
    return {
      name,
      timestamp: Date.now(),
      memoryStats: stats,
      gcStats,
      trackingData: new Map(this.memoryLeakDetection)
    };
  }

  /**
   * 比较内存快照
   */
  compareSnapshots(snapshot1: MemorySnapshot, snapshot2: MemorySnapshot): MemoryComparison {
    const memoryDiff = {
      used: snapshot2.memoryStats.used - snapshot1.memoryStats.used,
      heapUsed: snapshot2.memoryStats.heapUsed - snapshot1.memoryStats.heapUsed,
      external: snapshot2.memoryStats.external - snapshot1.memoryStats.external,
      rss: snapshot2.memoryStats.rss - snapshot1.memoryStats.rss
    };

    const gcDiff = {
      count: snapshot2.gcStats.count - snapshot1.gcStats.count,
      timeDiff: snapshot2.timestamp - snapshot1.timestamp
    };

    return {
      snapshot1: snapshot1.name,
      snapshot2: snapshot2.name,
      timeDiff: snapshot2.timestamp - snapshot1.timestamp,
      memoryDiff,
      gcDiff,
      hasMemoryLeak: memoryDiff.heapUsed > 100 // 超过100MB认为可能有内存泄漏
    };
  }
}

/**
 * 内存快照
 */
export interface MemorySnapshot {
  name: string;
  timestamp: number;
  memoryStats: MemoryStats;
  gcStats: ReturnType<MemoryManager['getGCStats']>;
  trackingData: Map<string, number>;
}

/**
 * 内存比较结果
 */
export interface MemoryComparison {
  snapshot1: string;
  snapshot2: string;
  timeDiff: number;
  memoryDiff: {
    used: number;
    heapUsed: number;
    external: number;
    rss: number;
  };
  gcDiff: {
    count: number;
    timeDiff: number;
  };
  hasMemoryLeak: boolean;
}

// 全局内存管理器实例
export const memoryManager = new MemoryManager();
