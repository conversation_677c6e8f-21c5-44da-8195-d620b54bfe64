import WebSocket from 'ws';
import { IncomingMessage } from 'http';
import { getLogger } from '../utils/logger';
import { AuthManager } from '../auth/auth-manager';
import { StreamManager } from '../stream/manager';

const logger = getLogger();

/**
 * 消息类型
 */
export enum MessageType {
  // 认证相关
  AUTH = 'auth',
  AUTH_SUCCESS = 'auth_success',
  AUTH_FAILED = 'auth_failed',
  
  // 信令相关
  OFFER = 'offer',
  ANSWER = 'answer',
  ICE_CANDIDATE = 'ice_candidate',
  
  // 流管理
  JOIN_STREAM = 'join_stream',
  LEAVE_STREAM = 'leave_stream',
  STREAM_JOINED = 'stream_joined',
  STREAM_LEFT = 'stream_left',
  
  // 事件通知
  STREAM_CREATED = 'stream_created',
  STREAM_DELETED = 'stream_deleted',
  PUBLISHER_JOINED = 'publisher_joined',
  PUBLISHER_LEFT = 'publisher_left',
  SUBSCRIBER_JOINED = 'subscriber_joined',
  SUBSCRIBER_LEFT = 'subscriber_left',
  
  // 质量监控
  QUALITY_UPDATE = 'quality_update',
  NETWORK_STATUS = 'network_status',
  
  // 错误处理
  ERROR = 'error',
  
  // 心跳
  PING = 'ping',
  PONG = 'pong'
}

/**
 * WebSocket消息
 */
export interface WSMessage {
  type: MessageType;
  data?: any;
  streamId?: string;
  sessionId?: string;
  timestamp?: number;
}

/**
 * 客户端连接信息
 */
export interface ClientConnection {
  id: string;
  ws: WebSocket;
  authenticated: boolean;
  userId?: string;
  streamId?: string;
  role?: 'publisher' | 'subscriber';
  lastPing: number;
  metadata?: Record<string, any>;
}

/**
 * WebSocket信令服务器
 */
export class SignalingServer {
  private wss: WebSocket.Server;
  private clients = new Map<string, ClientConnection>();
  private streamClients = new Map<string, Set<string>>(); // streamId -> clientIds
  private pingInterval?: NodeJS.Timeout;
  private pingIntervalMs = 30000; // 30秒心跳间隔
  private pongTimeoutMs = 10000; // 10秒pong超时

  constructor(
    private authManager: AuthManager,
    private streamManager: StreamManager,
    private port: number = 8081
  ) {
    this.wss = new WebSocket.Server({ 
      port: this.port,
      verifyClient: this.verifyClient.bind(this)
    });
    
    this.setupEventHandlers();
    this.startPingInterval();
    
    logger.info(`WebSocket信令服务器启动，端口: ${this.port}`);
  }

  /**
   * 验证客户端连接
   */
  private verifyClient(info: { origin: string; secure: boolean; req: IncomingMessage }): boolean {
    // 这里可以添加额外的验证逻辑
    return true;
  }

  /**
   * 设置事件处理器
   */
  private setupEventHandlers(): void {
    this.wss.on('connection', (ws: WebSocket, req: IncomingMessage) => {
      const clientId = this.generateClientId();
      const client: ClientConnection = {
        id: clientId,
        ws,
        authenticated: false,
        lastPing: Date.now()
      };
      
      this.clients.set(clientId, client);
      logger.debug(`WebSocket客户端连接: ${clientId}`);

      // 设置消息处理
      ws.on('message', (data: WebSocket.Data) => {
        this.handleMessage(clientId, data);
      });

      // 设置连接关闭处理
      ws.on('close', (code: number, reason: string) => {
        this.handleDisconnection(clientId, code, reason);
      });

      // 设置错误处理
      ws.on('error', (error: Error) => {
        logger.error(`WebSocket客户端错误 ${clientId}: ${error.message}`);
        this.handleDisconnection(clientId, 1006, 'error');
      });

      // 发送连接确认
      this.sendMessage(clientId, {
        type: MessageType.AUTH,
        data: { message: '请进行身份认证' },
        timestamp: Date.now()
      });
    });

    this.wss.on('error', (error: Error) => {
      logger.error(`WebSocket服务器错误: ${error.message}`);
    });
  }

  /**
   * 处理消息
   */
  private async handleMessage(clientId: string, data: WebSocket.Data): Promise<void> {
    const client = this.clients.get(clientId);
    if (!client) {
      return;
    }

    try {
      const message: WSMessage = JSON.parse(data.toString());
      logger.debug(`收到消息 ${clientId}: ${message.type}`);

      // 更新最后活动时间
      client.lastPing = Date.now();

      switch (message.type) {
        case MessageType.AUTH:
          await this.handleAuth(clientId, message);
          break;
        
        case MessageType.JOIN_STREAM:
          await this.handleJoinStream(clientId, message);
          break;
        
        case MessageType.LEAVE_STREAM:
          await this.handleLeaveStream(clientId, message);
          break;
        
        case MessageType.OFFER:
        case MessageType.ANSWER:
        case MessageType.ICE_CANDIDATE:
          await this.handleSignaling(clientId, message);
          break;
        
        case MessageType.PING:
          this.handlePing(clientId);
          break;
        
        default:
          logger.warn(`未知消息类型: ${message.type}`);
          this.sendError(clientId, `未知消息类型: ${message.type}`);
      }
    } catch (error) {
      logger.error(`处理消息失败 ${clientId}: ${error}`);
      this.sendError(clientId, '消息格式错误');
    }
  }

  /**
   * 处理认证
   */
  private async handleAuth(clientId: string, message: WSMessage): Promise<void> {
    const client = this.clients.get(clientId);
    if (!client) {
      return;
    }

    try {
      const { token } = message.data || {};
      if (!token) {
        this.sendMessage(clientId, {
          type: MessageType.AUTH_FAILED,
          data: { error: '缺少认证token' },
          timestamp: Date.now()
        });
        return;
      }

      const payload = this.authManager.verifyToken(token);
      if (!payload) {
        this.sendMessage(clientId, {
          type: MessageType.AUTH_FAILED,
          data: { error: '无效的认证token' },
          timestamp: Date.now()
        });
        return;
      }

      client.authenticated = true;
      client.userId = payload.userId;
      client.metadata = message.data?.metadata;

      this.sendMessage(clientId, {
        type: MessageType.AUTH_SUCCESS,
        data: { 
          userId: payload.userId,
          username: payload.username 
        },
        timestamp: Date.now()
      });

      logger.info(`客户端认证成功: ${clientId} (${payload.username})`);
    } catch (error) {
      logger.error(`认证处理失败: ${error}`);
      this.sendError(clientId, '认证处理失败');
    }
  }

  /**
   * 处理加入流
   */
  private async handleJoinStream(clientId: string, message: WSMessage): Promise<void> {
    const client = this.clients.get(clientId);
    if (!client || !client.authenticated) {
      this.sendError(clientId, '未认证');
      return;
    }

    const { streamId, role } = message.data || {};
    if (!streamId || !role) {
      this.sendError(clientId, '缺少streamId或role参数');
      return;
    }

    try {
      // 检查流是否存在
      let stream = this.streamManager.getStream(streamId);
      if (!stream) {
        // 自动创建流
        stream = this.streamManager.createStream(streamId);
      }

      // 更新客户端信息
      client.streamId = streamId;
      client.role = role;

      // 添加到流客户端列表
      if (!this.streamClients.has(streamId)) {
        this.streamClients.set(streamId, new Set());
      }
      this.streamClients.get(streamId)!.add(clientId);

      // 发送加入成功消息
      this.sendMessage(clientId, {
        type: MessageType.STREAM_JOINED,
        streamId,
        data: { 
          role,
          stream: {
            id: stream.id,
            created: stream.created,
            publish: stream.publish,
            subscribers: stream.subscribers
          }
        },
        timestamp: Date.now()
      });

      // 通知其他客户端
      this.broadcastToStream(streamId, {
        type: role === 'publisher' ? MessageType.PUBLISHER_JOINED : MessageType.SUBSCRIBER_JOINED,
        streamId,
        data: { 
          clientId,
          userId: client.userId,
          metadata: client.metadata 
        },
        timestamp: Date.now()
      }, clientId);

      logger.info(`客户端加入流: ${clientId} -> ${streamId} (${role})`);
    } catch (error) {
      logger.error(`加入流失败: ${error}`);
      this.sendError(clientId, '加入流失败');
    }
  }

  /**
   * 处理离开流
   */
  private async handleLeaveStream(clientId: string, message: WSMessage): Promise<void> {
    const client = this.clients.get(clientId);
    if (!client || !client.streamId) {
      return;
    }

    const streamId = client.streamId;
    const role = client.role;

    // 从流客户端列表中移除
    const streamClients = this.streamClients.get(streamId);
    if (streamClients) {
      streamClients.delete(clientId);
      if (streamClients.size === 0) {
        this.streamClients.delete(streamId);
      }
    }

    // 清除客户端流信息
    client.streamId = undefined;
    client.role = undefined;

    // 发送离开确认
    this.sendMessage(clientId, {
      type: MessageType.STREAM_LEFT,
      streamId,
      timestamp: Date.now()
    });

    // 通知其他客户端
    this.broadcastToStream(streamId, {
      type: role === 'publisher' ? MessageType.PUBLISHER_LEFT : MessageType.SUBSCRIBER_LEFT,
      streamId,
      data: { 
        clientId,
        userId: client.userId 
      },
      timestamp: Date.now()
    }, clientId);

    logger.info(`客户端离开流: ${clientId} <- ${streamId} (${role})`);
  }

  /**
   * 处理信令消息
   */
  private async handleSignaling(clientId: string, message: WSMessage): Promise<void> {
    const client = this.clients.get(clientId);
    if (!client || !client.streamId) {
      this.sendError(clientId, '未加入流');
      return;
    }

    // 转发信令消息给流中的其他客户端
    this.broadcastToStream(client.streamId, message, clientId);
  }

  /**
   * 处理心跳
   */
  private handlePing(clientId: string): void {
    const client = this.clients.get(clientId);
    if (client) {
      client.lastPing = Date.now();
      this.sendMessage(clientId, {
        type: MessageType.PONG,
        timestamp: Date.now()
      });
    }
  }

  /**
   * 处理断开连接
   */
  private handleDisconnection(clientId: string, code: number, reason: string): void {
    const client = this.clients.get(clientId);
    if (!client) {
      return;
    }

    // 如果客户端在流中，先离开流
    if (client.streamId) {
      this.handleLeaveStream(clientId, { type: MessageType.LEAVE_STREAM });
    }

    // 移除客户端
    this.clients.delete(clientId);
    
    logger.info(`WebSocket客户端断开: ${clientId} (${code}: ${reason})`);
  }

  /**
   * 发送消息给客户端
   */
  private sendMessage(clientId: string, message: WSMessage): void {
    const client = this.clients.get(clientId);
    if (!client || client.ws.readyState !== WebSocket.OPEN) {
      return;
    }

    try {
      client.ws.send(JSON.stringify(message));
    } catch (error) {
      logger.error(`发送消息失败 ${clientId}: ${error}`);
    }
  }

  /**
   * 发送错误消息
   */
  private sendError(clientId: string, error: string): void {
    this.sendMessage(clientId, {
      type: MessageType.ERROR,
      data: { error },
      timestamp: Date.now()
    });
  }

  /**
   * 广播消息给流中的客户端
   */
  private broadcastToStream(streamId: string, message: WSMessage, excludeClientId?: string): void {
    const streamClients = this.streamClients.get(streamId);
    if (!streamClients) {
      return;
    }

    for (const clientId of streamClients) {
      if (clientId !== excludeClientId) {
        this.sendMessage(clientId, message);
      }
    }
  }

  /**
   * 广播消息给所有客户端
   */
  public broadcast(message: WSMessage, excludeClientId?: string): void {
    for (const clientId of this.clients.keys()) {
      if (clientId !== excludeClientId) {
        this.sendMessage(clientId, message);
      }
    }
  }

  /**
   * 生成客户端ID
   */
  private generateClientId(): string {
    return `client_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * 启动心跳检查
   */
  private startPingInterval(): void {
    this.pingInterval = setInterval(() => {
      const now = Date.now();
      
      for (const [clientId, client] of this.clients.entries()) {
        // 检查是否超时
        if (now - client.lastPing > this.pingIntervalMs + this.pongTimeoutMs) {
          logger.warn(`客户端心跳超时: ${clientId}`);
          client.ws.terminate();
          this.handleDisconnection(clientId, 1000, 'ping timeout');
          continue;
        }
        
        // 发送心跳
        if (now - client.lastPing > this.pingIntervalMs) {
          this.sendMessage(clientId, {
            type: MessageType.PING,
            timestamp: now
          });
        }
      }
    }, this.pingIntervalMs);
  }

  /**
   * 获取服务器统计信息
   */
  public getStats() {
    const totalClients = this.clients.size;
    const authenticatedClients = Array.from(this.clients.values()).filter(c => c.authenticated).length;
    const streamCount = this.streamClients.size;
    
    const streamStats = Array.from(this.streamClients.entries()).map(([streamId, clients]) => ({
      streamId,
      clientCount: clients.size
    }));

    return {
      totalClients,
      authenticatedClients,
      streamCount,
      streams: streamStats
    };
  }

  /**
   * 关闭服务器
   */
  public close(): void {
    if (this.pingInterval) {
      clearInterval(this.pingInterval);
    }

    // 关闭所有客户端连接
    for (const client of this.clients.values()) {
      client.ws.close(1001, 'Server shutting down');
    }

    // 关闭WebSocket服务器
    this.wss.close(() => {
      logger.info('WebSocket信令服务器已关闭');
    });
  }
}
