import { getLogger } from '../utils/logger';
import { dataChannelManager, DataChannelMessage, MessageType } from './data-channel-manager';

const logger = getLogger();

/**
 * 协议消息类型
 */
export enum ProtocolMessageType {
  // 控制消息
  HANDSHAKE = 'handshake',
  HEARTBEAT = 'heartbeat',
  ACK = 'ack',
  ERROR = 'error',
  
  // 媒体控制
  MEDIA_CONTROL = 'media_control',
  QUALITY_REQUEST = 'quality_request',
  BITRATE_CHANGE = 'bitrate_change',
  
  // 流控制
  STREAM_START = 'stream_start',
  STREAM_STOP = 'stream_stop',
  STREAM_PAUSE = 'stream_pause',
  STREAM_RESUME = 'stream_resume',
  
  // 数据传输
  FILE_TRANSFER = 'file_transfer',
  CHAT_MESSAGE = 'chat_message',
  CUSTOM_DATA = 'custom_data',
  
  // 状态同步
  STATE_SYNC = 'state_sync',
  PRESENCE_UPDATE = 'presence_update'
}

/**
 * 协议消息
 */
export interface ProtocolMessage {
  type: ProtocolMessageType;
  id: string;
  timestamp: number;
  payload: any;
  requiresAck?: boolean;
  replyTo?: string;
}

/**
 * 文件传输信息
 */
export interface FileTransferInfo {
  id: string;
  name: string;
  size: number;
  type: string;
  chunks: number;
  checksum?: string;
}

/**
 * 文件块
 */
export interface FileChunk {
  transferId: string;
  chunkIndex: number;
  data: ArrayBuffer;
  isLast: boolean;
}

/**
 * 媒体控制命令
 */
export interface MediaControlCommand {
  action: 'mute' | 'unmute' | 'pause' | 'resume' | 'stop';
  mediaType: 'audio' | 'video' | 'both';
  parameters?: Record<string, any>;
}

/**
 * 质量请求
 */
export interface QualityRequest {
  targetBitrate?: number;
  targetFrameRate?: number;
  targetResolution?: { width: number; height: number };
  adaptiveMode?: boolean;
}

/**
 * 应用层协议处理器
 */
export class ApplicationProtocolHandler {
  private messageHandlers = new Map<ProtocolMessageType, Function[]>();
  private pendingAcks = new Map<string, { resolve: Function; reject: Function; timeout: NodeJS.Timeout }>();
  private fileTransfers = new Map<string, { info: FileTransferInfo; chunks: Map<number, ArrayBuffer>; receivedChunks: number }>();
  private ackTimeout = 5000; // 5秒ACK超时

  constructor() {
    // 注册全局消息处理器
    dataChannelManager.registerGlobalMessageHandler(this.handleDataChannelMessage.bind(this));
    
    // 注册内置消息处理器
    this.registerBuiltinHandlers();
    
    logger.info('应用层协议处理器已初始化');
  }

  /**
   * 发送协议消息
   */
  async sendMessage(
    channelId: string,
    type: ProtocolMessageType,
    payload: any,
    requiresAck: boolean = false
  ): Promise<void> {
    const messageId = this.generateMessageId();
    
    const message: ProtocolMessage = {
      type,
      id: messageId,
      timestamp: Date.now(),
      payload,
      requiresAck
    };

    try {
      await dataChannelManager.sendMessage(channelId, message, MessageType.JSON);
      
      // 如果需要ACK，等待确认
      if (requiresAck) {
        await this.waitForAck(messageId);
      }
      
      logger.debug(`发送协议消息: ${type}, 通道: ${channelId}`);
    } catch (error) {
      logger.error(`发送协议消息失败: ${type}, 错误: ${error}`);
      throw error;
    }
  }

  /**
   * 广播协议消息
   */
  async broadcastMessage(
    sessionId: string,
    type: ProtocolMessageType,
    payload: any,
    excludeChannelId?: string
  ): Promise<void> {
    const channels = dataChannelManager.getSessionChannels(sessionId);
    
    const promises = channels
      .filter(channel => channel.id !== excludeChannelId)
      .map(channel => 
        this.sendMessage(channel.id, type, payload).catch(error => {
          logger.warn(`广播消息失败: ${channel.id}, 错误: ${error}`);
        })
      );

    await Promise.allSettled(promises);
  }

  /**
   * 发送媒体控制命令
   */
  async sendMediaControl(
    channelId: string,
    command: MediaControlCommand
  ): Promise<void> {
    await this.sendMessage(channelId, ProtocolMessageType.MEDIA_CONTROL, command, true);
  }

  /**
   * 发送质量请求
   */
  async sendQualityRequest(
    channelId: string,
    request: QualityRequest
  ): Promise<void> {
    await this.sendMessage(channelId, ProtocolMessageType.QUALITY_REQUEST, request, true);
  }

  /**
   * 发送聊天消息
   */
  async sendChatMessage(
    channelId: string,
    message: string,
    metadata?: Record<string, any>
  ): Promise<void> {
    await this.sendMessage(channelId, ProtocolMessageType.CHAT_MESSAGE, {
      message,
      metadata
    });
  }

  /**
   * 开始文件传输
   */
  async startFileTransfer(
    channelId: string,
    file: File | { name: string; data: ArrayBuffer; type: string }
  ): Promise<string> {
    const transferId = this.generateMessageId();
    const data = file instanceof File ? await file.arrayBuffer() : file.data;
    const chunkSize = 16384; // 16KB chunks
    const chunks = Math.ceil(data.byteLength / chunkSize);
    
    const fileInfo: FileTransferInfo = {
      id: transferId,
      name: file.name,
      size: data.byteLength,
      type: file instanceof File ? file.type : file.type,
      chunks
    };

    // 发送文件传输开始消息
    await this.sendMessage(channelId, ProtocolMessageType.FILE_TRANSFER, {
      action: 'start',
      fileInfo
    }, true);

    // 发送文件块
    for (let i = 0; i < chunks; i++) {
      const start = i * chunkSize;
      const end = Math.min(start + chunkSize, data.byteLength);
      const chunkData = data.slice(start, end);
      
      const chunk: FileChunk = {
        transferId,
        chunkIndex: i,
        data: chunkData,
        isLast: i === chunks - 1
      };

      await dataChannelManager.sendMessage(channelId, chunk, MessageType.BINARY);
      
      // 小延迟以避免拥塞
      if (i % 10 === 0) {
        await new Promise(resolve => setTimeout(resolve, 10));
      }
    }

    logger.info(`文件传输开始: ${transferId} (${file.name})`);
    return transferId;
  }

  /**
   * 发送状态同步
   */
  async sendStateSync(
    channelId: string,
    state: Record<string, any>
  ): Promise<void> {
    await this.sendMessage(channelId, ProtocolMessageType.STATE_SYNC, state);
  }

  /**
   * 发送在线状态更新
   */
  async sendPresenceUpdate(
    sessionId: string,
    presence: {
      status: 'online' | 'away' | 'busy' | 'offline';
      message?: string;
      metadata?: Record<string, any>;
    }
  ): Promise<void> {
    await this.broadcastMessage(sessionId, ProtocolMessageType.PRESENCE_UPDATE, presence);
  }

  /**
   * 注册消息处理器
   */
  registerMessageHandler(type: ProtocolMessageType, handler: (payload: any, channelId: string) => Promise<void> | void): void {
    if (!this.messageHandlers.has(type)) {
      this.messageHandlers.set(type, []);
    }
    this.messageHandlers.get(type)!.push(handler);
  }

  /**
   * 移除消息处理器
   */
  removeMessageHandler(type: ProtocolMessageType, handler: Function): void {
    const handlers = this.messageHandlers.get(type);
    if (handlers) {
      const index = handlers.indexOf(handler);
      if (index !== -1) {
        handlers.splice(index, 1);
      }
    }
  }

  /**
   * 处理数据通道消息
   */
  private async handleDataChannelMessage(message: DataChannelMessage): Promise<void> {
    try {
      // 检查是否是协议消息
      if (message.type === MessageType.JSON && this.isProtocolMessage(message.data)) {
        await this.handleProtocolMessage(message.data, message.channelId);
      } else if (message.type === MessageType.BINARY && this.isFileChunk(message.data)) {
        await this.handleFileChunk(message.data, message.channelId);
      }
    } catch (error) {
      logger.error(`处理协议消息失败: ${error}`);
    }
  }

  /**
   * 处理协议消息
   */
  private async handleProtocolMessage(protocolMessage: ProtocolMessage, channelId: string): Promise<void> {
    // 发送ACK（如果需要）
    if (protocolMessage.requiresAck) {
      await this.sendAck(channelId, protocolMessage.id);
    }

    // 处理ACK消息
    if (protocolMessage.type === ProtocolMessageType.ACK) {
      this.handleAck(protocolMessage.replyTo!);
      return;
    }

    // 调用注册的处理器
    const handlers = this.messageHandlers.get(protocolMessage.type) || [];
    for (const handler of handlers) {
      try {
        await handler(protocolMessage.payload, channelId);
      } catch (error) {
        logger.error(`协议消息处理器错误: ${protocolMessage.type}, 错误: ${error}`);
      }
    }
  }

  /**
   * 处理文件块
   */
  private async handleFileChunk(chunk: FileChunk, channelId: string): Promise<void> {
    const transfer = this.fileTransfers.get(chunk.transferId);
    if (!transfer) {
      logger.warn(`收到未知文件传输的块: ${chunk.transferId}`);
      return;
    }

    // 存储块
    transfer.chunks.set(chunk.chunkIndex, chunk.data);
    transfer.receivedChunks++;

    // 检查是否接收完成
    if (chunk.isLast && transfer.receivedChunks === transfer.info.chunks) {
      await this.completeFileTransfer(chunk.transferId, channelId);
    }
  }

  /**
   * 完成文件传输
   */
  private async completeFileTransfer(transferId: string, channelId: string): Promise<void> {
    const transfer = this.fileTransfers.get(transferId);
    if (!transfer) {
      return;
    }

    // 重组文件
    const chunks = Array.from({ length: transfer.info.chunks }, (_, i) => 
      transfer.chunks.get(i)!
    );
    
    const fileData = new Uint8Array(transfer.info.size);
    let offset = 0;
    
    for (const chunk of chunks) {
      fileData.set(new Uint8Array(chunk), offset);
      offset += chunk.byteLength;
    }

    // 触发文件接收完成事件
    const handlers = this.messageHandlers.get(ProtocolMessageType.FILE_TRANSFER) || [];
    for (const handler of handlers) {
      try {
        await handler({
          action: 'complete',
          transferId,
          fileInfo: transfer.info,
          data: fileData.buffer
        }, channelId);
      } catch (error) {
        logger.error(`文件传输完成处理器错误: ${error}`);
      }
    }

    // 清理传输记录
    this.fileTransfers.delete(transferId);
    
    logger.info(`文件传输完成: ${transferId} (${transfer.info.name})`);
  }

  /**
   * 发送ACK
   */
  private async sendAck(channelId: string, messageId: string): Promise<void> {
    const ackMessage: ProtocolMessage = {
      type: ProtocolMessageType.ACK,
      id: this.generateMessageId(),
      timestamp: Date.now(),
      payload: {},
      replyTo: messageId
    };

    await dataChannelManager.sendMessage(channelId, ackMessage, MessageType.JSON);
  }

  /**
   * 等待ACK
   */
  private waitForAck(messageId: string): Promise<void> {
    return new Promise((resolve, reject) => {
      const timeout = setTimeout(() => {
        this.pendingAcks.delete(messageId);
        reject(new Error(`ACK超时: ${messageId}`));
      }, this.ackTimeout);

      this.pendingAcks.set(messageId, { resolve, reject, timeout });
    });
  }

  /**
   * 处理ACK
   */
  private handleAck(messageId: string): void {
    const pending = this.pendingAcks.get(messageId);
    if (pending) {
      clearTimeout(pending.timeout);
      pending.resolve();
      this.pendingAcks.delete(messageId);
    }
  }

  /**
   * 注册内置处理器
   */
  private registerBuiltinHandlers(): void {
    // 心跳处理器
    this.registerMessageHandler(ProtocolMessageType.HEARTBEAT, async (payload, channelId) => {
      // 回复心跳
      await this.sendMessage(channelId, ProtocolMessageType.HEARTBEAT, {
        reply: true,
        timestamp: Date.now()
      });
    });

    // 文件传输开始处理器
    this.registerMessageHandler(ProtocolMessageType.FILE_TRANSFER, async (payload, channelId) => {
      if (payload.action === 'start') {
        this.fileTransfers.set(payload.fileInfo.id, {
          info: payload.fileInfo,
          chunks: new Map(),
          receivedChunks: 0
        });
        logger.info(`开始接收文件: ${payload.fileInfo.name}`);
      }
    });
  }

  /**
   * 检查是否是协议消息
   */
  private isProtocolMessage(data: any): data is ProtocolMessage {
    return data && 
           typeof data === 'object' && 
           typeof data.type === 'string' && 
           typeof data.id === 'string' && 
           typeof data.timestamp === 'number' &&
           data.payload !== undefined;
  }

  /**
   * 检查是否是文件块
   */
  private isFileChunk(data: any): data is FileChunk {
    return data && 
           typeof data === 'object' && 
           typeof data.transferId === 'string' && 
           typeof data.chunkIndex === 'number' && 
           data.data instanceof ArrayBuffer &&
           typeof data.isLast === 'boolean';
  }

  /**
   * 生成消息ID
   */
  private generateMessageId(): string {
    return `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * 获取统计信息
   */
  getStats() {
    return {
      pendingAcks: this.pendingAcks.size,
      activeFileTransfers: this.fileTransfers.size,
      registeredHandlers: Array.from(this.messageHandlers.entries()).map(([type, handlers]) => ({
        type,
        handlerCount: handlers.length
      }))
    };
  }

  /**
   * 停止协议处理器
   */
  stop(): void {
    // 清理所有待处理的ACK
    for (const [messageId, pending] of this.pendingAcks.entries()) {
      clearTimeout(pending.timeout);
      pending.reject(new Error('协议处理器已停止'));
    }
    this.pendingAcks.clear();

    // 清理文件传输
    this.fileTransfers.clear();

    // 清理消息处理器
    this.messageHandlers.clear();

    logger.info('应用层协议处理器已停止');
  }
}

// 全局应用层协议处理器实例
export const appProtocolHandler = new ApplicationProtocolHandler();
