// Jest测试设置文件

// 设置测试超时
jest.setTimeout(10000);

// 模拟全局对象
global.console = {
  ...console,
  // 在测试中静默某些日志
  log: jest.fn(),
  debug: jest.fn(),
  info: jest.fn(),
  warn: jest.fn(),
  error: jest.fn()
};

// 模拟WebRTC API
const mockRTCPeerConnection = jest.fn().mockImplementation(() => ({
  connectionState: 'new',
  iceConnectionState: 'new',
  iceGatheringState: 'new',
  signalingState: 'stable',
  localDescription: null,
  remoteDescription: null,
  
  createOffer: jest.fn().mockResolvedValue({
    type: 'offer',
    sdp: 'mock-sdp-offer'
  }),
  
  createAnswer: jest.fn().mockResolvedValue({
    type: 'answer',
    sdp: 'mock-sdp-answer'
  }),
  
  setLocalDescription: jest.fn().mockResolvedValue(undefined),
  setRemoteDescription: jest.fn().mockResolvedValue(undefined),
  
  addIceCandidate: jest.fn().mockResolvedValue(undefined),
  
  addTrack: jest.fn().mockReturnValue({
    track: null,
    streams: []
  }),
  
  removeTrack: jest.fn(),
  
  getSenders: jest.fn().mockReturnValue([]),
  getReceivers: jest.fn().mockReturnValue([]),
  
  getStats: jest.fn().mockResolvedValue(new Map()),
  
  close: jest.fn(),
  
  addEventListener: jest.fn(),
  removeEventListener: jest.fn(),
  
  dispatchEvent: jest.fn()
}));

// 模拟MediaStream API
const mockMediaStream = jest.fn().mockImplementation(() => ({
  id: 'mock-stream-id',
  active: true,
  getTracks: jest.fn().mockReturnValue([]),
  getAudioTracks: jest.fn().mockReturnValue([]),
  getVideoTracks: jest.fn().mockReturnValue([]),
  addTrack: jest.fn(),
  removeTrack: jest.fn(),
  clone: jest.fn(),
  addEventListener: jest.fn(),
  removeEventListener: jest.fn()
}));

// 模拟MediaStreamTrack API
const mockMediaStreamTrack = jest.fn().mockImplementation(() => ({
  id: 'mock-track-id',
  kind: 'video',
  label: 'mock-track',
  enabled: true,
  muted: false,
  readyState: 'live',
  stop: jest.fn(),
  clone: jest.fn(),
  addEventListener: jest.fn(),
  removeEventListener: jest.fn()
}));

// 将模拟对象添加到全局作用域
(global as any).RTCPeerConnection = mockRTCPeerConnection;
(global as any).MediaStream = mockMediaStream;
(global as any).MediaStreamTrack = mockMediaStreamTrack;

// 模拟WebSocket
const mockWebSocket = jest.fn().mockImplementation(() => ({
  readyState: 1, // OPEN
  send: jest.fn(),
  close: jest.fn(),
  addEventListener: jest.fn(),
  removeEventListener: jest.fn()
}));

(global as any).WebSocket = mockWebSocket;

// 模拟process.env
process.env.NODE_ENV = 'test';
process.env.LOG_LEVEL = 'ERROR'; // 在测试中减少日志输出

// 清理函数
afterEach(() => {
  jest.clearAllMocks();
});

// 全局错误处理
process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason);
});

process.on('uncaughtException', (error) => {
  console.error('Uncaught Exception:', error);
});
