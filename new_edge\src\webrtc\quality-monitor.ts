import { getLogger } from '../utils/logger';
import { webrtcStatsCollector, MediaType, MediaQuality, NetworkQuality, ConnectionStats } from './stats-collector';

const logger = getLogger();

/**
 * 质量阈值配置
 */
export interface QualityThresholds {
  video: {
    excellent: { packetsLost: number; frameRate: number; bitrate: number };
    good: { packetsLost: number; frameRate: number; bitrate: number };
    fair: { packetsLost: number; frameRate: number; bitrate: number };
    poor: { packetsLost: number; frameRate: number; bitrate: number };
  };
  audio: {
    excellent: { packetsLost: number; jitter: number; bitrate: number };
    good: { packetsLost: number; jitter: number; bitrate: number };
    fair: { packetsLost: number; jitter: number; bitrate: number };
    poor: { packetsLost: number; jitter: number; bitrate: number };
  };
  network: {
    excellent: { rtt: number; bandwidth: number };
    good: { rtt: number; bandwidth: number };
    fair: { rtt: number; bandwidth: number };
    poor: { rtt: number; bandwidth: number };
  };
}

/**
 * 质量事件类型
 */
export enum QualityEventType {
  QUALITY_DEGRADED = 'quality_degraded',
  QUALITY_IMPROVED = 'quality_improved',
  NETWORK_ISSUE = 'network_issue',
  MEDIA_ISSUE = 'media_issue',
  CONNECTION_ISSUE = 'connection_issue'
}

/**
 * 质量事件
 */
export interface QualityEvent {
  type: QualityEventType;
  connectionId: string;
  mediaType?: MediaType;
  previousQuality?: MediaQuality | NetworkQuality;
  currentQuality?: MediaQuality | NetworkQuality;
  details: any;
  timestamp: number;
}

/**
 * 质量报告
 */
export interface QualityReport {
  connectionId: string;
  overallQuality: MediaQuality;
  videoQuality?: MediaQuality;
  audioQuality?: MediaQuality;
  networkQuality: NetworkQuality;
  issues: string[];
  recommendations: string[];
  timestamp: number;
}

/**
 * 媒体质量监控器
 */
export class MediaQualityMonitor {
  private thresholds: QualityThresholds;
  private previousQualities = new Map<string, { video?: MediaQuality; audio?: MediaQuality; network?: NetworkQuality }>();
  private qualityHistory = new Map<string, QualityReport[]>();
  private eventListeners = new Map<QualityEventType, Function[]>();
  private monitoringIntervals = new Map<string, NodeJS.Timeout>();

  constructor(thresholds?: Partial<QualityThresholds>) {
    this.thresholds = {
      video: {
        excellent: { packetsLost: 0.5, frameRate: 25, bitrate: 1000 },
        good: { packetsLost: 2, frameRate: 20, bitrate: 500 },
        fair: { packetsLost: 5, frameRate: 15, bitrate: 200 },
        poor: { packetsLost: 10, frameRate: 10, bitrate: 100 }
      },
      audio: {
        excellent: { packetsLost: 0.1, jitter: 10, bitrate: 64 },
        good: { packetsLost: 1, jitter: 30, bitrate: 32 },
        fair: { packetsLost: 3, jitter: 50, bitrate: 16 },
        poor: { packetsLost: 8, jitter: 100, bitrate: 8 }
      },
      network: {
        excellent: { rtt: 50, bandwidth: 2000 },
        good: { rtt: 100, bandwidth: 1000 },
        fair: { rtt: 200, bandwidth: 500 },
        poor: { rtt: 400, bandwidth: 200 }
      },
      ...thresholds
    };

    logger.info('媒体质量监控器已初始化');
  }

  /**
   * 开始监控连接
   */
  startMonitoring(connectionId: string, intervalMs: number = 5000): void {
    // 停止现有监控
    this.stopMonitoring(connectionId);

    // 开始统计收集
    webrtcStatsCollector.startCollecting(connectionId, intervalMs);

    // 设置质量监控间隔
    const interval = setInterval(() => {
      this.analyzeQuality(connectionId);
    }, intervalMs);

    this.monitoringIntervals.set(connectionId, interval);
    logger.debug(`开始监控连接质量: ${connectionId}`);
  }

  /**
   * 停止监控连接
   */
  stopMonitoring(connectionId: string): void {
    const interval = this.monitoringIntervals.get(connectionId);
    if (interval) {
      clearInterval(interval);
      this.monitoringIntervals.delete(connectionId);
    }

    webrtcStatsCollector.stopCollecting(connectionId);
    this.previousQualities.delete(connectionId);
    
    logger.debug(`停止监控连接质量: ${connectionId}`);
  }

  /**
   * 分析连接质量
   */
  analyzeQuality(connectionId: string): QualityReport | null {
    const stats = webrtcStatsCollector.getLatestStats(connectionId);
    if (!stats) {
      return null;
    }

    const report = this.generateQualityReport(connectionId, stats);
    
    // 保存报告历史
    const history = this.qualityHistory.get(connectionId) || [];
    history.push(report);
    
    // 限制历史记录长度
    if (history.length > 50) {
      history.shift();
    }
    
    this.qualityHistory.set(connectionId, history);

    // 检查质量变化
    this.checkQualityChanges(connectionId, report);

    return report;
  }

  /**
   * 生成质量报告
   */
  private generateQualityReport(connectionId: string, stats: ConnectionStats): QualityReport {
    const videoStats = stats.mediaStats.find(s => s.type === MediaType.VIDEO);
    const audioStats = stats.mediaStats.find(s => s.type === MediaType.AUDIO);
    
    const videoQuality = videoStats?.quality;
    const audioQuality = audioStats?.quality;
    const networkQuality = stats.networkStats.quality;

    // 计算整体质量
    const qualities = [videoQuality, audioQuality, networkQuality].filter(q => q !== undefined);
    const overallQuality = this.calculateOverallQuality(qualities as MediaQuality[]);

    // 识别问题和建议
    const issues = this.identifyIssues(stats);
    const recommendations = this.generateRecommendations(stats, issues);

    return {
      connectionId,
      overallQuality,
      videoQuality,
      audioQuality,
      networkQuality,
      issues,
      recommendations,
      timestamp: Date.now()
    };
  }

  /**
   * 计算整体质量
   */
  private calculateOverallQuality(qualities: MediaQuality[]): MediaQuality {
    if (qualities.length === 0) {
      return MediaQuality.FAIR;
    }

    // 使用最差的质量作为整体质量
    const qualityOrder = [
      MediaQuality.BAD,
      MediaQuality.POOR,
      MediaQuality.FAIR,
      MediaQuality.GOOD,
      MediaQuality.EXCELLENT
    ];

    let worstQuality = MediaQuality.EXCELLENT;
    for (const quality of qualities) {
      const currentIndex = qualityOrder.indexOf(quality);
      const worstIndex = qualityOrder.indexOf(worstQuality);
      if (currentIndex < worstIndex) {
        worstQuality = quality;
      }
    }

    return worstQuality;
  }

  /**
   * 识别问题
   */
  private identifyIssues(stats: ConnectionStats): string[] {
    const issues: string[] = [];

    // 检查连接状态
    if (stats.connectionState !== 'connected') {
      issues.push(`连接状态异常: ${stats.connectionState}`);
    }

    if (stats.iceConnectionState !== 'connected') {
      issues.push(`ICE连接状态异常: ${stats.iceConnectionState}`);
    }

    // 检查媒体问题
    for (const mediaStats of stats.mediaStats) {
      if (mediaStats.packetsLostPercentage && mediaStats.packetsLostPercentage > 5) {
        issues.push(`${mediaStats.type}丢包率过高: ${mediaStats.packetsLostPercentage.toFixed(2)}%`);
      }

      if (mediaStats.type === MediaType.VIDEO) {
        if (mediaStats.frameRate && mediaStats.frameRate < 15) {
          issues.push(`视频帧率过低: ${mediaStats.frameRate}fps`);
        }
        if (mediaStats.framesDropped && mediaStats.framesDropped > 0) {
          issues.push(`视频帧丢失: ${mediaStats.framesDropped}帧`);
        }
      }

      if (mediaStats.type === MediaType.AUDIO) {
        if (mediaStats.jitter && mediaStats.jitter > 50) {
          issues.push(`音频抖动过高: ${mediaStats.jitter}ms`);
        }
      }
    }

    // 检查网络问题
    const rtt = stats.networkStats.candidatePair?.currentRoundTripTime;
    if (rtt && rtt > 0.2) {
      issues.push(`网络延迟过高: ${(rtt * 1000).toFixed(0)}ms`);
    }

    const bandwidth = stats.networkStats.candidatePair?.availableOutgoingBitrate;
    if (bandwidth && bandwidth < 500000) {
      issues.push(`可用带宽不足: ${(bandwidth / 1000).toFixed(0)}kbps`);
    }

    return issues;
  }

  /**
   * 生成建议
   */
  private generateRecommendations(stats: ConnectionStats, issues: string[]): string[] {
    const recommendations: string[] = [];

    // 基于问题生成建议
    for (const issue of issues) {
      if (issue.includes('丢包率过高')) {
        recommendations.push('建议降低视频质量或检查网络连接');
      } else if (issue.includes('帧率过低')) {
        recommendations.push('建议降低视频分辨率或提高网络带宽');
      } else if (issue.includes('延迟过高')) {
        recommendations.push('建议检查网络连接或使用更近的服务器');
      } else if (issue.includes('带宽不足')) {
        recommendations.push('建议降低媒体质量或优化网络连接');
      } else if (issue.includes('抖动过高')) {
        recommendations.push('建议检查网络稳定性或使用有线连接');
      }
    }

    // 通用建议
    if (recommendations.length === 0 && issues.length > 0) {
      recommendations.push('建议检查网络连接和设备性能');
    }

    return recommendations;
  }

  /**
   * 检查质量变化
   */
  private checkQualityChanges(connectionId: string, report: QualityReport): void {
    const previous = this.previousQualities.get(connectionId);
    if (!previous) {
      this.previousQualities.set(connectionId, {
        video: report.videoQuality,
        audio: report.audioQuality,
        network: report.networkQuality
      });
      return;
    }

    // 检查视频质量变化
    if (report.videoQuality && previous.video && report.videoQuality !== previous.video) {
      this.emitQualityEvent({
        type: this.getQualityChangeType(previous.video, report.videoQuality),
        connectionId,
        mediaType: MediaType.VIDEO,
        previousQuality: previous.video,
        currentQuality: report.videoQuality,
        details: { report },
        timestamp: Date.now()
      });
    }

    // 检查音频质量变化
    if (report.audioQuality && previous.audio && report.audioQuality !== previous.audio) {
      this.emitQualityEvent({
        type: this.getQualityChangeType(previous.audio, report.audioQuality),
        connectionId,
        mediaType: MediaType.AUDIO,
        previousQuality: previous.audio,
        currentQuality: report.audioQuality,
        details: { report },
        timestamp: Date.now()
      });
    }

    // 检查网络质量变化
    if (previous.network && report.networkQuality !== previous.network) {
      this.emitQualityEvent({
        type: this.getQualityChangeType(previous.network, report.networkQuality),
        connectionId,
        previousQuality: previous.network,
        currentQuality: report.networkQuality,
        details: { report },
        timestamp: Date.now()
      });
    }

    // 更新之前的质量
    this.previousQualities.set(connectionId, {
      video: report.videoQuality,
      audio: report.audioQuality,
      network: report.networkQuality
    });
  }

  /**
   * 获取质量变化类型
   */
  private getQualityChangeType(previous: MediaQuality | NetworkQuality, current: MediaQuality | NetworkQuality): QualityEventType {
    const qualityOrder = [
      MediaQuality.BAD,
      MediaQuality.POOR,
      MediaQuality.FAIR,
      MediaQuality.GOOD,
      MediaQuality.EXCELLENT
    ];

    const previousIndex = qualityOrder.indexOf(previous as MediaQuality);
    const currentIndex = qualityOrder.indexOf(current as MediaQuality);

    return currentIndex > previousIndex ? QualityEventType.QUALITY_IMPROVED : QualityEventType.QUALITY_DEGRADED;
  }

  /**
   * 发出质量事件
   */
  private emitQualityEvent(event: QualityEvent): void {
    const listeners = this.eventListeners.get(event.type) || [];
    listeners.forEach(listener => {
      try {
        listener(event);
      } catch (error) {
        logger.error(`质量事件监听器错误: ${error}`);
      }
    });

    logger.debug(`质量事件: ${event.type}, 连接: ${event.connectionId}`);
  }

  /**
   * 添加事件监听器
   */
  addEventListener(type: QualityEventType, listener: (event: QualityEvent) => void): void {
    if (!this.eventListeners.has(type)) {
      this.eventListeners.set(type, []);
    }
    this.eventListeners.get(type)!.push(listener);
  }

  /**
   * 移除事件监听器
   */
  removeEventListener(type: QualityEventType, listener: (event: QualityEvent) => void): void {
    const listeners = this.eventListeners.get(type);
    if (listeners) {
      const index = listeners.indexOf(listener);
      if (index !== -1) {
        listeners.splice(index, 1);
      }
    }
  }

  /**
   * 获取质量历史
   */
  getQualityHistory(connectionId: string): QualityReport[] {
    return this.qualityHistory.get(connectionId) || [];
  }

  /**
   * 获取最新质量报告
   */
  getLatestQualityReport(connectionId: string): QualityReport | null {
    const history = this.qualityHistory.get(connectionId);
    return history && history.length > 0 ? history[history.length - 1] : null;
  }

  /**
   * 停止所有监控
   */
  stopAllMonitoring(): void {
    for (const connectionId of this.monitoringIntervals.keys()) {
      this.stopMonitoring(connectionId);
    }
    
    this.qualityHistory.clear();
    this.previousQualities.clear();
    this.eventListeners.clear();
    
    logger.info('已停止所有质量监控');
  }
}

// 全局媒体质量监控器实例
export const mediaQualityMonitor = new MediaQualityMonitor();
