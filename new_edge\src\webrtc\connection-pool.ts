import { getLogger } from '../utils/logger';
import { ConnectionPool, ConnectionFactory } from '../utils/connection-pool';

const logger = getLogger();

/**
 * WebRTC连接工厂
 */
export class WebRTCConnectionFactory implements ConnectionFactory<RTCPeerConnection> {
  constructor(private config: RTCConfiguration) {}

  /**
   * 创建WebRTC连接
   */
  async create(): Promise<RTCPeerConnection> {
    const connection = new RTCPeerConnection(this.config);
    
    // 监听连接状态变化
    connection.addEventListener('connectionstatechange', () => {
      logger.debug(`WebRTC连接状态变化: ${connection.connectionState}`);
    });

    // 监听ICE连接状态变化
    connection.addEventListener('iceconnectionstatechange', () => {
      logger.debug(`ICE连接状态变化: ${connection.iceConnectionState}`);
    });

    // 监听信令状态变化
    connection.addEventListener('signalingstatechange', () => {
      logger.debug(`信令状态变化: ${connection.signalingState}`);
    });

    return connection;
  }

  /**
   * 验证WebRTC连接
   */
  async validate(connection: RTCPeerConnection): Promise<boolean> {
    // 检查连接状态
    if (connection.connectionState === 'failed' || 
        connection.connectionState === 'closed' ||
        connection.iceConnectionState === 'failed' ||
        connection.iceConnectionState === 'closed') {
      return false;
    }
    
    return true;
  }

  /**
   * 销毁WebRTC连接
   */
  async destroy(connection: RTCPeerConnection): Promise<void> {
    try {
      // 关闭所有发送器
      const senders = connection.getSenders();
      for (const sender of senders) {
        if (sender.track) {
          sender.track.stop();
        }
      }

      // 关闭所有接收器
      const receivers = connection.getReceivers();
      for (const receiver of receivers) {
        if (receiver.track) {
          receiver.track.stop();
        }
      }

      // 关闭连接
      connection.close();
    } catch (error) {
      logger.error(`销毁WebRTC连接失败: ${error}`);
    }
  }
}

/**
 * WebRTC连接池
 */
export class WebRTCConnectionPool {
  private publisherPool: ConnectionPool<RTCPeerConnection>;
  private subscriberPool: ConnectionPool<RTCPeerConnection>;

  constructor(config: RTCConfiguration) {
    const factory = new WebRTCConnectionFactory(config);
    
    // 创建发布者连接池
    this.publisherPool = new ConnectionPool<RTCPeerConnection>(factory, {
      maxConnections: 100,
      minConnections: 5,
      acquireTimeout: 10000,
      idleTimeout: 60000,
      maxRetries: 3
    });
    
    // 创建订阅者连接池
    this.subscriberPool = new ConnectionPool<RTCPeerConnection>(factory, {
      maxConnections: 500,
      minConnections: 10,
      acquireTimeout: 10000,
      idleTimeout: 60000,
      maxRetries: 3
    });
    
    logger.info('WebRTC连接池已初始化');
  }

  /**
   * 获取发布者连接
   */
  async getPublisherConnection(): Promise<RTCPeerConnection> {
    const pooledConnection = await this.publisherPool.acquire();
    return pooledConnection.connection;
  }

  /**
   * 获取订阅者连接
   */
  async getSubscriberConnection(): Promise<RTCPeerConnection> {
    const pooledConnection = await this.subscriberPool.acquire();
    return pooledConnection.connection;
  }

  /**
   * 释放发布者连接
   */
  async releasePublisherConnection(connection: RTCPeerConnection): Promise<void> {
    // 查找连接
    const connections = await this.getPoolStats();
    const pooledConnection = connections.publisherConnections.find(
      conn => conn.connection === connection
    );
    
    if (pooledConnection) {
      this.publisherPool.release(pooledConnection);
    } else {
      logger.warn('尝试释放未知的发布者连接');
    }
  }

  /**
   * 释放订阅者连接
   */
  async releaseSubscriberConnection(connection: RTCPeerConnection): Promise<void> {
    // 查找连接
    const connections = await this.getPoolStats();
    const pooledConnection = connections.subscriberConnections.find(
      conn => conn.connection === connection
    );
    
    if (pooledConnection) {
      this.subscriberPool.release(pooledConnection);
    } else {
      logger.warn('尝试释放未知的订阅者连接');
    }
  }

  /**
   * 获取连接池统计信息
   */
  async getPoolStats() {
    const publisherStats = this.publisherPool.getStats();
    const subscriberStats = this.subscriberPool.getStats();
    
    return {
      publisher: publisherStats,
      subscriber: subscriberStats,
      publisherConnections: [] as any[],
      subscriberConnections: [] as any[]
    };
  }

  /**
   * 关闭连接池
   */
  async shutdown(): Promise<void> {
    await Promise.all([
      this.publisherPool.shutdown(),
      this.subscriberPool.shutdown()
    ]);
    
    logger.info('WebRTC连接池已关闭');
  }
}
